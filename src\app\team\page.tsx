'use client';

import Link from 'next/link';

export default function Team() {
  const teamMembers = [
    {
      name: "<PERSON>",
      position: "Chief Executive Officer",
      bio: "With over 20 years in global finance and impact investing, <PERSON> leads our vision of sustainable economic development.",
      initials: "<PERSON>"
    },
    {
      name: "<PERSON>",
      position: "Chief Investment Officer",
      bio: "<PERSON> brings 18 years of private equity experience, specializing in emerging markets and ESG investments.",
      initials: "<PERSON>"
    },
    {
      name: "<PERSON>",
      position: "Head of Project Development",
      bio: "<PERSON> oversees our global project portfolio, ensuring maximum impact and sustainable outcomes.",
      initials: "<PERSON>"
    },
    {
      name: "<PERSON>",
      position: "Chief Financial Officer",
      bio: "<PERSON> manages our financial operations and risk management across all investment vehicles.",
      initials: "<PERSON><PERSON>"
    },
    {
      name: "<PERSON>. <PERSON>",
      position: "Head of Impact Assessment",
      bio: "<PERSON><PERSON> leads our impact measurement and evaluation, ensuring our investments create meaningful change.",
      initials: "<PERSON><PERSON>"
    },
    {
      name: "<PERSON>",
      position: "Managing Director, Asia Pacific",
      bio: "<PERSON> leads our expansion across Asia Pacific markets with deep regional expertise.",
      initials: "<PERSON><PERSON>"
    }
  ];

  return (
    <div className="font-sans text-gray-800 bg-white">
      {/* Navigation */}
      <nav className="fixed w-full bg-white bg-opacity-95 shadow-md z-50 transition-all duration-300">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-deep-teal font-serif font-bold text-xl">EmBrace Holdings</span>
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</Link>
              <Link href="/about" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">About</Link>
              <Link href="/services" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</Link>
              <Link href="/team" className="nav-link text-deep-teal font-medium">Team</Link>
              <Link href="/contact" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Contact</Link>
              <Link href="/investor-portal" className="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                Investor Portal
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero-pattern min-h-screen flex items-center pt-20">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 py-20">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
              Our Leadership Team
            </h1>
            <div className="h-1 w-24 gold-gradient mb-6 mx-auto"></div>
            <p className="text-lg md:text-xl opacity-90 mb-8 max-w-3xl mx-auto">
              Meet the experienced professionals driving our mission of sustainable impact investing across the globe.
            </p>
          </div>
        </div>
      </section>

      {/* Team Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow">
                <div className="text-center">
                  <div className="w-24 h-24 rounded-full bg-deep-teal flex items-center justify-center text-white font-bold text-2xl mx-auto mb-6">
                    {member.initials}
                  </div>
                  <h3 className="text-xl font-serif font-bold text-deep-teal mb-2">{member.name}</h3>
                  <p className="text-royal-gold font-medium mb-4">{member.position}</p>
                  <p className="text-gray-600">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Leadership Philosophy */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Our Leadership Philosophy</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Collaborative Leadership</h3>
              <p className="text-gray-600">
                We believe in the power of diverse perspectives and collaborative decision-making to drive innovation and impact.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Ethical Excellence</h3>
              <p className="text-gray-600">
                Our leaders are committed to the highest standards of integrity, transparency, and ethical business practices.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Visionary Thinking</h3>
              <p className="text-gray-600">
                We combine long-term strategic vision with practical execution to create sustainable value and lasting impact.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Join Our Team */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-6">Join Our Team</h2>
          <p className="text-gray-600 max-w-2xl mx-auto mb-8">
            We're always looking for talented individuals who share our passion for creating positive impact through strategic investments.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/careers" className="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-lg transition-all">
              View Open Positions
            </Link>
            <Link href="/contact" className="bg-transparent border-2 border-deep-teal text-deep-teal px-8 py-3 rounded-full font-medium hover:bg-deep-teal hover:text-white transition-all">
              Contact HR
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-midnight-black text-white py-16">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-2 mb-6">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-white font-serif font-bold text-xl">EmBrace Holdings</span>
            </Link>
            <p className="text-gray-300 mb-6">
              © 2024 EmBrace Holdings Enterprise LLC. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
