import type { <PERSON>ada<PERSON>, Viewport } from 'next';
import { Mont<PERSON><PERSON>, Playfair_Display } from 'next/font/google';
import { Layout } from '@/components/Layout';
import { Providers } from '@/components/Providers';
import './globals.css';

// Fonts
const montserrat = Montserrat({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-montserrat',
});

const playfair = Playfair_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-playfair',
});

// Base URL for metadata (used for generating absolute URLs)
const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

// Metadata
export const metadata: Metadata = {
  metadataBase: new URL(baseUrl),
  title: {
    default: 'EmBrace Holdings | Strategic Investments for a Better Tomorrow',
    template: '%s | EmBrace Holdings',
  },
  description: 'EmBrace Holdings is a global investment firm dedicated to creating sustainable impact through strategic investments in emerging markets and innovative solutions.',
  keywords: [
    'impact investing',
    'sustainable development',
    'emerging markets',
    'private equity',
    'social impact',
    'ESG investing',
    'global development',
    'humanitarian investment'
  ],
  authors: [{ name: 'EmBrace Holdings' }],
  creator: 'EmBrace Holdings',
  publisher: 'EmBrace Holdings',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'EmBrace Holdings | Strategic Investments for a Better Tomorrow',
    description: 'Creating sustainable impact through strategic investments in emerging markets and innovative solutions.',
    url: 'https://embraceholdings.com',
    siteName: 'EmBrace Holdings',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'EmBrace Holdings - Strategic Investments for a Better Tomorrow',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image' as const,
    title: 'EmBrace Holdings | Strategic Investments for a Better Tomorrow',
    description: 'Creating sustainable impact through strategic investments in emerging markets and innovative solutions.',
    images: ['/images/twitter-card.jpg'],
  },
};

// Viewport configuration
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#F7FAFC' },
    { media: '(prefers-color-scheme: dark)', color: '#0A192F' },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${montserrat.variable} ${playfair.variable}`} suppressHydrationWarning>
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Font Awesome */}
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </head>
      <body className="min-h-screen bg-off-white text-charcoal transition-colors duration-200">
        <Providers>
          <Layout>
            {children}
          </Layout>
        </Providers>
      </body>
    </html>
  );
}
