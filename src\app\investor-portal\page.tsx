'use client';

import Link from 'next/link';

export default function InvestorPortal() {
  return (
    <div className="font-sans text-gray-800 bg-white">
      {/* Navigation */}
      <nav className="fixed w-full bg-white bg-opacity-95 shadow-md z-50 transition-all duration-300">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-deep-teal font-serif font-bold text-xl">Kaiteur Equity Partners</span>
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</Link>
              <Link href="/about" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">About</Link>
              <Link href="/services" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</Link>
              <Link href="/team" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Team</Link>
              <Link href="/contact" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Contact</Link>
              <Link href="/investor-portal" className="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                Investor Portal
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero-pattern min-h-screen flex items-center pt-20">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 py-20">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
              Investor Portal
            </h1>
            <div className="h-1 w-24 gold-gradient mb-6 mx-auto"></div>
            <p className="text-lg md:text-xl opacity-90 mb-8 max-w-3xl mx-auto">
              Access exclusive investment opportunities and manage your portfolio with Kaiteur Equity Partners.
            </p>
          </div>
        </div>
      </section>

      {/* Login Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-md mx-auto">
            <div className="bg-gray-50 rounded-lg p-8 shadow-lg">
              <h2 className="text-2xl font-serif font-bold text-deep-teal mb-6 text-center">Secure Login</h2>
              
              <form className="space-y-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                  <input type="email" id="email" name="email" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="<EMAIL>" />
                </div>
                
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">Password</label>
                  <input type="password" id="password" name="password" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="••••••••" />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox" className="h-4 w-4 text-deep-teal focus:ring-deep-teal border-gray-300 rounded" />
                    <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                      Remember me
                    </label>
                  </div>
                  
                  <div className="text-sm">
                    <a href="#" className="font-medium text-deep-teal hover:text-royal-gold">
                      Forgot your password?
                    </a>
                  </div>
                </div>
                
                <button type="submit" className="w-full gold-gradient text-midnight-black font-bold py-3 px-6 rounded-lg hover:shadow-lg transition-all">
                  Sign In
                </button>
              </form>
              
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  Don't have an account?{' '}
                  <a href="#" className="font-medium text-deep-teal hover:text-royal-gold">
                    Request Access
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Portal Features</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Portfolio Dashboard</h3>
              <p className="text-gray-600">
                Real-time portfolio performance tracking with detailed analytics and reporting.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Investment Opportunities</h3>
              <p className="text-gray-600">
                Exclusive access to new investment opportunities and detailed due diligence materials.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Reports & Documents</h3>
              <p className="text-gray-600">
                Access to quarterly reports, impact assessments, and important investor communications.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-6">Need Help?</h2>
          <p className="text-gray-600 max-w-2xl mx-auto mb-8">
            Our investor relations team is here to assist you with any questions about your account or investment opportunities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-lg transition-all">
              Contact Support
            </Link>
            <a href="mailto:<EMAIL>" className="bg-transparent border-2 border-deep-teal text-deep-teal px-8 py-3 rounded-full font-medium hover:bg-deep-teal hover:text-white transition-all">
              Email Us
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-midnight-black text-white py-16">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-2 mb-6">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-white font-serif font-bold text-xl">Kaiteur Equity Partners</span>
            </Link>
            <p className="text-gray-300 mb-6">
              © 2024 Kaiteur Equity Partners. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
