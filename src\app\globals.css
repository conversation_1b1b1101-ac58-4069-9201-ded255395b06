/* Custom properties */
:root {
  /* Colors */
  --deep-teal: #005A5B;
  --light-teal: #007D7E;
  --royal-gold: #D4AF37;
  --midnight-black: #121212;
  --off-white: #F7FAFC;
  --charcoal: #2D3748;

  /* Typography */
  --font-montserrat: 'Montserrat', sans-serif;
  --font-playfair: 'Playfair Display', serif;
}

/* Tailwind CSS custom classes */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Base styles */
html {
  scroll-behavior: smooth;
  box-sizing: border-box;
}

*, *:before, *:after {
  box-sizing: inherit;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-montserrat);
  color: var(--charcoal);
  background-color: var(--off-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: antialiased;
  line-height: 1.6;
}

/* Hero Pattern */
.hero-pattern {
  background-color: var(--deep-teal);
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Gold Gradient */
.gold-gradient {
  background: linear-gradient(135deg, #D4AF37 0%, #FFDF00 50%, #D4AF37 100%);
}

/* Section divider */
.section-divider {
  height: 4px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
}

/* Navigation link effects */
.nav-link {
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(90deg, #D4AF37, #FFDF00);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

/* Service card hover effects */
.service-card {
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Testimonial slider */
.testimonial-slider {
  scroll-behavior: smooth;
}

/* Globe animation */
.globe-icon {
  position: relative;
  z-index: 10;
}

.globe-bg {
  position: absolute;
  width: 150%;
  height: 150%;
  top: -25%;
  left: -25%;
  background: radial-gradient(circle, rgba(0,90,91,0.2) 0%, rgba(0,90,91,0) 70%);
  border-radius: 50%;
  z-index: 5;
  animation: pulse 4s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(0.8); opacity: 0.3; }
  50% { transform: scale(1); opacity: 0.5; }
  100% { transform: scale(0.8); opacity: 0.3; }
}

/* Animations */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-playfair);
  font-weight: 700;
  margin-top: 0;
  line-height: 1.2;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
  line-height: 1.6;
}

a {
  color: var(--deep-teal);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--royal-gold);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section {
  padding: 5rem 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 2rem;
  border-radius: 0.375rem;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  font-size: 1rem;
  line-height: 1.5;
}

.btn-primary {
  background-color: var(--deep-teal);
  color: white;
}

.btn-primary:hover {
  background-color: #006b76;
  transform: translateY(-1px);
}

.btn-gold {
  background: linear-gradient(90deg, #D4AF37 0%, #F4D03F 100%);
  color: var(--midnight-black);
  font-weight: 700;
}

.btn-gold:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Utility classes */
.text-gold {
  color: var(--royal-gold);
}

.bg-pattern {
  background-color: var(--deep-teal);
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Responsive typography */
@media (min-width: 768px) {
  .text-display {
    font-size: 3.5rem;
    line-height: 1.1;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--deep-teal);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #006b76;
}
