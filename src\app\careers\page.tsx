'use client';

import Link from 'next/link';

export default function Careers() {
  const openPositions = [
    {
      title: "Senior Investment Analyst",
      department: "Investment Team",
      location: "New York, NY",
      type: "Full-time"
    },
    {
      title: "ESG Research Manager",
      department: "Impact Assessment",
      location: "London, UK",
      type: "Full-time"
    },
    {
      title: "Project Development Director",
      department: "Project Development",
      location: "Singapore",
      type: "Full-time"
    },
    {
      title: "Financial Analyst",
      department: "Finance",
      location: "New York, NY",
      type: "Full-time"
    }
  ];

  return (
    <div className="font-sans text-gray-800 bg-white">
      {/* Navigation */}
      <nav className="fixed w-full bg-white bg-opacity-95 shadow-md z-50 transition-all duration-300">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-deep-teal font-serif font-bold text-xl">Kaiteur Equity Partners</span>
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</Link>
              <Link href="/about" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">About</Link>
              <Link href="/services" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</Link>
              <Link href="/team" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Team</Link>
              <Link href="/contact" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Contact</Link>
              <Link href="/investor-portal" className="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                Investor Portal
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero-pattern min-h-screen flex items-center pt-20">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 py-20">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
              Join Our Team
            </h1>
            <div className="h-1 w-24 gold-gradient mb-6 mx-auto"></div>
            <p className="text-lg md:text-xl opacity-90 mb-8 max-w-3xl mx-auto">
              Build your career with a purpose-driven organization that's making a real difference in the world.
            </p>
          </div>
        </div>
      </section>

      {/* Why Work With Us */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Why Kaiteur Equity Partners?</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Meaningful Impact</h3>
              <p className="text-gray-600">
                Work on projects that create real positive change in communities around the world.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Career Growth</h3>
              <p className="text-gray-600">
                Accelerate your career with mentorship, training, and opportunities for advancement.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Global Team</h3>
              <p className="text-gray-600">
                Collaborate with talented professionals from diverse backgrounds across the globe.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Open Positions</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
          </div>
          
          <div className="space-y-6">
            {openPositions.map((position, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="mb-4 md:mb-0">
                    <h3 className="text-xl font-serif font-bold text-deep-teal mb-2">{position.title}</h3>
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        {position.department}
                      </span>
                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        {position.location}
                      </span>
                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {position.type}
                      </span>
                    </div>
                  </div>
                  <div className="flex space-x-4">
                    <button className="bg-transparent border-2 border-deep-teal text-deep-teal px-6 py-2 rounded-full font-medium hover:bg-deep-teal hover:text-white transition-all">
                      Learn More
                    </button>
                    <button className="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                      Apply Now
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Benefits & Perks</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h4 className="font-bold text-deep-teal mb-2">Health & Wellness</h4>
              <p className="text-gray-600 text-sm">Comprehensive health insurance and wellness programs</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-bold text-deep-teal mb-2">Competitive Salary</h4>
              <p className="text-gray-600 text-sm">Market-leading compensation and performance bonuses</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h4 className="font-bold text-deep-teal mb-2">Learning & Development</h4>
              <p className="text-gray-600 text-sm">Continuous learning opportunities and skill development</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-bold text-deep-teal mb-2">Global Opportunities</h4>
              <p className="text-gray-600 text-sm">Work across international markets and cultures</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-6">Ready to Make an Impact?</h2>
          <p className="text-gray-600 max-w-2xl mx-auto mb-8">
            Don't see the perfect role? We're always interested in hearing from talented individuals who share our mission.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-lg transition-all">
              Send Your Resume
            </Link>
            <a href="mailto:<EMAIL>" className="bg-transparent border-2 border-deep-teal text-deep-teal px-8 py-3 rounded-full font-medium hover:bg-deep-teal hover:text-white transition-all">
              Email HR Team
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-midnight-black text-white py-16">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-2 mb-6">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-white font-serif font-bold text-xl">Kaiteur Equity Partners</span>
            </Link>
            <p className="text-gray-300 mb-6">
              © 2024 Kaiteur Equity Partners. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
