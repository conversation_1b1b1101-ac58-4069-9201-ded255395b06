'use client';

import Link from 'next/link';

export default function About() {
  return (
    <div className="font-sans text-gray-800 bg-white">
      {/* Navigation */}
      <nav className="fixed w-full bg-white bg-opacity-95 shadow-md z-50 transition-all duration-300">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-deep-teal font-serif font-bold text-xl">Kaiteur Equity Partners</span>
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</Link>
              <Link href="/about" className="nav-link text-deep-teal font-medium">About</Link>
              <Link href="/services" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</Link>
              <Link href="/team" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Team</Link>
              <Link href="/contact" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Contact</Link>
              <Link href="/investor-portal" className="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                Investor Portal
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero-pattern min-h-screen flex items-center pt-20">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 py-20">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
              About Kaiteur Equity Partners
            </h1>
            <div className="h-1 w-24 gold-gradient mb-6 mx-auto"></div>
            <p className="text-lg md:text-xl opacity-90 mb-8 max-w-3xl mx-auto">
              Driving economic development through strategic investments and humanitarian initiatives across the globe.
            </p>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-6">Our Story</h2>
              <p className="text-gray-600 mb-6">
                Founded in 2009, Kaiteur Equity Partners emerged from a vision to bridge the gap between financial excellence and humanitarian impact. Our founders recognized that sustainable economic development requires more than just capital—it demands a deep understanding of local communities, cultural nuances, and long-term social impact.
              </p>
              <p className="text-gray-600 mb-6">
                Over the past 15 years, we have grown from a small investment firm to a global leader in impact investing, managing over $2.7 billion in assets across 30+ countries. Our success is measured not just in financial returns, but in the positive change we create in communities worldwide.
              </p>
              <p className="text-gray-600 mb-8">
                Today, Kaiteur Equity Partners stands as a testament to the power of purpose-driven investing, proving that financial success and social impact are not mutually exclusive, but rather complementary forces that drive sustainable growth.
              </p>
            </div>
            
            <div className="relative">
              <div className="bg-deep-teal rounded-lg p-8 text-white relative z-10">
                <h3 className="text-2xl font-serif font-bold mb-6">Company Milestones</h3>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-4 h-4 rounded-full bg-royal-gold mt-2 mr-4 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-bold mb-1">2009 - Foundation</h4>
                      <p className="text-gray-200">Kaiteur Equity Partners established with $50M initial capital</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-4 h-4 rounded-full bg-royal-gold mt-2 mr-4 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-bold mb-1">2012 - Global Expansion</h4>
                      <p className="text-gray-200">Opened offices in London, Dubai, and Singapore</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-4 h-4 rounded-full bg-royal-gold mt-2 mr-4 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-bold mb-1">2018 - $1B Milestone</h4>
                      <p className="text-gray-200">Reached $1 billion in assets under management</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-4 h-4 rounded-full bg-royal-gold mt-2 mr-4 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-bold mb-1">2024 - Impact Leader</h4>
                      <p className="text-gray-200">Managing $2.7B+ with 5M+ lives impacted globally</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="absolute top-4 right-4 -z-10 w-full h-full bg-royal-gold opacity-20 rounded-lg transform rotate-3"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Mission & Vision</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div className="bg-white rounded-lg p-8 shadow-lg border-l-4 border-deep-teal">
              <div className="text-deep-teal mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-2xl font-serif font-bold text-deep-teal mb-4">Our Mission</h3>
              <p className="text-gray-600">
                To drive sustainable economic development through strategic investments that create lasting positive impact in communities worldwide, while delivering exceptional returns for our investors and stakeholders.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-8 shadow-lg border-l-4 border-royal-gold">
              <div className="text-royal-gold mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-2xl font-serif font-bold text-deep-teal mb-4">Our Vision</h3>
              <p className="text-gray-600">
                To be the world's leading impact investment firm, recognized for our ability to generate superior financial returns while creating meaningful social and environmental change across emerging markets.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Our Core Values</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Integrity</h3>
              <p className="text-gray-600">
                We conduct business with the highest ethical standards, transparency, and accountability in all our dealings.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Innovation</h3>
              <p className="text-gray-600">
                We embrace creative solutions and cutting-edge approaches to address complex global challenges.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-4">Impact</h3>
              <p className="text-gray-600">
                We measure success not just by financial returns, but by the positive change we create in communities.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-midnight-black text-white py-16">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-2 mb-6">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-white font-serif font-bold text-xl">Kaiteur Equity Partners</span>
            </Link>
            <p className="text-gray-300 mb-6">
              © 2024 Kaiteur Equity Partners. All rights reserved.
            </p>
            <div className="flex justify-center space-x-6">
              <Link href="/privacy" className="text-gray-300 hover:text-royal-gold transition-colors">Privacy Policy</Link>
              <Link href="/terms" className="text-gray-300 hover:text-royal-gold transition-colors">Terms of Service</Link>
              <Link href="/contact" className="text-gray-300 hover:text-royal-gold transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
