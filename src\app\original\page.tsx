import React from 'react';
import Head from 'next/head';
import <PERSON>ript from 'next/script';
import <PERSON> from 'next/link';

export default function OriginalPage() {
  // This effect will run on the client side
  React.useEffect(() => {
    // Mobile menu toggle functionality
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Navbar scroll effect
    const navbar = document.getElementById('navbar');
    if (navbar) {
      window.addEventListener('scroll', () => {
        if (window.scrollY > 10) {
          navbar.classList.add('bg-black', 'shadow-lg');
          navbar.classList.remove('bg-transparent');
        } else {
          navbar.classList.remove('bg-black', 'shadow-lg');
          navbar.classList.add('bg-transparent');
        }
      });
    }

    // Testimonial slider functionality
    const testimonials = document.querySelectorAll('.testimonial-item');
    const dots = document.querySelectorAll('.dot');
    const prevButton = document.getElementById('prev-testimonial');
    const nextButton = document.getElementById('next-testimonial');
    let currentIndex = 0;

    const updateSlider = () => {
      testimonials.forEach((testimonial, index) => {
        testimonial.classList.toggle('hidden', index !== currentIndex);
      });
      
      dots.forEach((dot, index) => {
        dot.classList.toggle('bg-gold-400', index === currentIndex);
        dot.classList.toggle('bg-gray-300', index !== currentIndex);
      });
    };

    if (prevButton && nextButton) {
      prevButton.addEventListener('click', () => {
        currentIndex = (currentIndex === 0) ? testimonials.length - 1 : currentIndex - 1;
        updateSlider();
      });

      nextButton.addEventListener('click', () => {
        currentIndex = (currentIndex === testimonials.length - 1) ? 0 : currentIndex + 1;
        updateSlider();
      });
    }

    // Auto-rotate testimonials
    const autoRotate = setInterval(() => {
      currentIndex = (currentIndex === testimonials.length - 1) ? 0 : currentIndex + 1;
      updateSlider();
    }, 5000);

    // Cleanup
    return () => {
      clearInterval(autoRotate);
      if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.removeEventListener('click', () => {});
      }
      if (navbar) {
        window.removeEventListener('scroll', () => {});
      }
      if (prevButton && nextButton) {
        prevButton.removeEventListener('click', () => {});
        nextButton.removeEventListener('click', () => {});
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Head>
        <title>EmBrace Holdings - Financing the Future of Humanity</title>
        <meta name="description" content="EmBrace Holdings - A global investment firm focused on innovative and sustainable solutions" />
        <link rel="icon" href="/favicon.ico" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
      </Head>

      {/* Navigation */}
      <nav id="navbar" className="fixed w-full z-50 transition-all duration-300 bg-transparent">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-white">
                <span className="text-gold-400">Em</span>Brace
              </Link>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#home" className="text-white hover:text-gold-400 transition-colors">Home</Link>
              <Link href="#about" className="text-white hover:text-gold-400 transition-colors">About Us</Link>
              <Link href="#services" className="text-white hover:text-gold-400 transition-colors">Services</Link>
              <Link href="#impact" className="text-white hover:text-gold-400 transition-colors">Global Impact</Link>
              <Link href="#contact" className="text-white hover:text-gold-400 transition-colors">Contact</Link>
              <Link href="#investor" className="bg-gradient-to-r from-yellow-500 to-amber-600 text-white px-6 py-2 rounded-full hover:opacity-90 transition-opacity">
                Investor Portal
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button id="mobile-menu-button" className="text-white focus:outline-none">
                <i className="fas fa-bars text-2xl"></i>
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          <div id="mobile-menu" className="md:hidden hidden mt-4 pb-4">
            <Link href="#home" className="block text-white py-2 hover:text-gold-400 transition-colors">Home</Link>
            <Link href="#about" className="block text-white py-2 hover:text-gold-400 transition-colors">About Us</Link>
            <Link href="#services" className="block text-white py-2 hover:text-gold-400 transition-colors">Services</Link>
            <Link href="#impact" className="block text-white py-2 hover:text-gold-400 transition-colors">Global Impact</Link>
            <Link href="#contact" className="block text-white py-2 hover:text-gold-400 transition-colors">Contact</Link>
            <Link href="#investor" className="inline-block mt-2 bg-gradient-to-r from-yellow-500 to-amber-600 text-white px-6 py-2 rounded-full hover:opacity-90 transition-opacity">
              Investor Portal
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="relative h-screen flex items-center justify-center bg-cover bg-center" 
        style={{
          backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url("/images/hero-bg.jpg")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}>
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 font-serif">
            Financing the Future of <span className="text-gold-400">Humanity</span>
          </h1>
          <p className="text-xl text-gray-300 mb-10 max-w-3xl mx-auto">
            EmBrace Holdings is a global investment firm dedicated to building a better future through strategic investments in innovative and sustainable solutions.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="#contact" className="bg-gradient-to-r from-yellow-500 to-amber-600 text-white px-8 py-4 rounded-full text-lg font-medium hover:opacity-90 transition-opacity">
              Get Started
            </Link>
            <Link href="#about" className="bg-white bg-opacity-10 text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-opacity-20 transition-all border border-white border-opacity-20">
              Learn More
            </Link>
          </div>
        </div>
        
        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div className="w-8 h-12 border-2 border-white rounded-2xl flex justify-center p-1">
            <div className="w-1 h-2 bg-white rounded-full animate-bounce"></div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="p-6">
              <div className="text-4xl md:text-5xl font-bold text-gold-600 mb-2">$2.5B+</div>
              <p className="text-gray-600">Assets Under Management</p>
            </div>
            <div className="p-6">
              <div className="text-4xl md:text-5xl font-bold text-gold-600 mb-2">50+</div>
              <p className="text-gray-600">Countries Served</p>
            </div>
            <div className="p-6">
              <div className="text-4xl md:text-5xl font-bold text-gold-600 mb-2">15+</div>
              <p className="text-gray-600">Years Experience</p>
            </div>
            <div className="p-6">
              <div className="text-4xl md:text-5xl font-bold text-gold-600 mb-2">100+</div>
              <p className="text-gray-600">Global Partners</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Preview */}
      <section id="about" className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4 font-serif">Who We Are</h2>
            <div className="w-20 h-1 bg-gold-500 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              EmBrace Holdings is a diversified global investment firm with a focus on innovative and sustainable solutions that drive positive change.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-10">
            <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-gold-100 rounded-full flex items-center justify-center mb-6 mx-auto">
                <i className="fas fa-lightbulb text-2xl text-gold-600"></i>
              </div>
              <h3 className="text-xl font-bold text-center mb-4">Our Vision</h3>
              <p className="text-gray-600 text-center">
                To be the leading catalyst for sustainable and impactful investments that shape a better future for all.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-gold-100 rounded-full flex items-center justify-center mb-6 mx-auto">
                <i className="fas fa-bullseye text-2xl text-gold-600"></i>
              </div>
              <h3 className="text-xl font-bold text-center mb-4">Our Mission</h3>
              <p className="text-gray-600 text-center">
                To identify and invest in innovative solutions that address global challenges while delivering superior returns.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-gold-100 rounded-full flex items-center justify-center mb-6 mx-auto">
                <i className="fas fa-hand-holding-heart text-2xl text-gold-600"></i>
              </div>
              <h3 className="text-xl font-bold text-center mb-4">Our Values</h3>
              <p className="text-gray-600 text-center">
                Integrity, Innovation, Impact, and Inclusivity guide everything we do at EmBrace Holdings.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4 font-serif">What Our Partners Say</h2>
            <div className="w-20 h-1 bg-gold-500 mx-auto mb-6"></div>
          </div>

          <div className="max-w-4xl mx-auto relative">
            {/* Testimonial Items */}
            <div className="relative h-64 overflow-hidden">
              {[
                {
                  quote: "Working with EmBrace Holdings has been transformative for our business. Their strategic insights and financial support have been invaluable.",
                  author: "Sarah Johnson",
                  title: "CEO, GreenTech Innovations"
                },
                {
                  quote: "The team at EmBrace brings not just capital, but a wealth of knowledge and a vast network that has helped us scale globally.",
                  author: "Michael Chen",
                  title: "Founder, NextGen Solutions"
                },
                {
                  quote: "Their commitment to sustainable and impactful investments aligns perfectly with our mission. A true partner in every sense.",
                  author: "Amina Diallo",
                  title: "Director, Africa Impact Fund"
                }
              ].map((testimonial, index) => (
                <div 
                  key={index}
                  className={`testimonial-item absolute inset-0 flex flex-col items-center justify-center transition-opacity duration-500 ${index === 0 ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                >
                  <div className="text-6xl text-gold-400 mb-4">"</div>
                  <p className="text-xl text-gray-700 text-center mb-6 max-w-2xl">{testimonial.quote}</p>
                  <div className="text-center">
                    <h4 className="font-bold text-lg">{testimonial.author}</h4>
                    <p className="text-gray-500">{testimonial.title}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Navigation Dots */}
            <div className="flex justify-center mt-8 space-x-2">
              {[0, 1, 2].map((index) => (
                <button 
                  key={index}
                  className={`dot w-3 h-3 rounded-full ${index === 0 ? 'bg-gold-400' : 'bg-gray-300'}`}
                  onClick={() => {
                    currentIndex = index;
                    updateSlider();
                  }}
                ></button>
              ))}
            </div>

            {/* Navigation Arrows */}
            <button 
              id="prev-testimonial" 
              className="absolute left-0 top-1/2 transform -translate-y-1/2 -ml-4 bg-white p-3 rounded-full shadow-lg hover:bg-gray-100 transition-colors"
            >
              <i className="fas fa-chevron-left text-gold-600"></i>
            </button>
            <button 
              id="next-testimonial" 
              className="absolute right-0 top-1/2 transform -translate-y-1/2 -mr-4 bg-white p-3 rounded-full shadow-lg hover:bg-gray-100 transition-colors"
            >
              <i className="fas fa-chevron-right text-gold-600"></i>
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white pt-16 pb-8">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div>
              <h3 className="text-2xl font-bold mb-4"><span className="text-gold-400">Em</span>Brace</h3>
              <p className="text-gray-400 mb-4">Financing the future of humanity through strategic investments and sustainable solutions.</p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fab fa-linkedin-in text-lg"></i>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fab fa-twitter text-lg"></i>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fab fa-facebook-f text-lg"></i>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fab fa-instagram text-lg"></i>
                </a>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="#home" className="text-gray-400 hover:text-white transition-colors">Home</a></li>
                <li><a href="#about" className="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#services" className="text-gray-400 hover:text-white transition-colors">Services</a></li>
                <li><a href="#impact" className="text-gray-400 hover:text-white transition-colors">Global Impact</a></li>
                <li><a href="#contact" className="text-gray-400 hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Our Services</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Investment Management</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Strategic Advisory</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Sustainable Finance</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Private Equity</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Venture Capital</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Newsletter</h4>
              <p className="text-gray-400 mb-4">Subscribe to our newsletter for the latest updates and insights.</p>
              <form className="flex">
                <input 
                  type="email" 
                  placeholder="Your email" 
                  className="bg-gray-800 text-white px-4 py-2 rounded-l-md focus:outline-none focus:ring-2 focus:ring-gold-500 w-full"
                />
                <button 
                  type="submit" 
                  className="bg-gradient-to-r from-yellow-500 to-amber-600 text-white px-4 py-2 rounded-r-md hover:opacity-90 transition-opacity"
                >
                  <i className="fas fa-paper-plane"></i>
                </button>
              </form>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} EmBrace Holdings. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Scripts */}
      <Script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" />
    </div>
  );
}
