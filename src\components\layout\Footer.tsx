import Link from 'next/link';
import { Icon } from '@/utils/icons';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-midnight-black text-white pt-16 pb-8">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
          {/* Company Info */}
          <div className="col-span-1">
            <h3 className="text-2xl font-serif font-bold text-royal-gold mb-4">EmBrace</h3>
            <p className="text-gray-400 mb-4">
              Financing the Future of Humanity through strategic investments and humanitarian initiatives.
            </p>
            <div className="flex space-x-4 mt-6">
              {['twitter', 'linkedin', 'facebook', 'instagram'].map((social) => (
                <a
                  key={social}
                  href={`#`}
                  className="text-gray-400 hover:text-royal-gold transition-colors"
                  aria-label={social}
                >
                  <span className="sr-only">{social}</span>
                  <i className={`fab fa-${social} text-xl`} />
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-3">
              {[
                { name: 'Home', path: '/' },
                { name: 'About Us', path: '/about' },
                { name: 'Services', path: '/services' },
                { name: 'Global Impact', path: '/impact' },
                { name: 'Contact', path: '/contact' },
              ].map((link) => (
                <li key={link.path}>
                  <Link 
                    href={link.path}
                    className="text-gray-400 hover:text-royal-gold transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Our Services</h4>
            <ul className="space-y-3">
              {[
                'Asset Management',
                'Private Equity',
                'Project Development',
                'Investment Advisory',
                'Strategic Consulting'
              ].map((service) => (
                <li key={service}>
                  <a href="#" className="text-gray-400 hover:text-royal-gold transition-colors">
                    {service}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Us</h4>
            <address className="not-italic text-gray-400 space-y-3">
              <p className="flex items-start">
                <i className="fas fa-map-marker-alt mt-1 mr-3 text-royal-gold"></i>
                <span>123 Business Avenue, Suite 100<br />New York, NY 10001</span>
              </p>
              <p className="flex items-center">
                <i className="fas fa-phone-alt mr-3 text-royal-gold"></i>
                <a href="tel:+***********" className="hover:text-royal-gold transition-colors">
                  +****************
                </a>
              </p>
              <p className="flex items-center">
                <i className="fas fa-envelope mr-3 text-royal-gold"></i>
                <a href="mailto:<EMAIL>" className="hover:text-royal-gold transition-colors">
                  <EMAIL>
                </a>
              </p>
            </address>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm mb-4 md:mb-0">
              &copy; {currentYear} EmBrace Holdings Enterprise LLC. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-500 hover:text-royal-gold text-sm transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-500 hover:text-royal-gold text-sm transition-colors">
                Terms of Service
              </a>
              <a href="#" className="text-gray-500 hover:text-royal-gold text-sm transition-colors">
                Cookies
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
