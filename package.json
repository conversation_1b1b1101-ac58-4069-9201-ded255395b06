{"name": "kep-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-brands-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "framer-motion": "^11.0.0", "next": "^14.2.30", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.5.3", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20.11.16", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}