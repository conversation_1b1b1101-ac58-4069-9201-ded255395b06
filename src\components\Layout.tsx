import { cn } from '@/lib/utils';
import { ThemeProvider } from './ThemeProvider';
import { Montserrat, Playfair_Display } from 'next/font/google';
import { ReactNode } from 'react';
import Head from 'next/head';

// Fonts
const montserrat = Montserrat({
  subsets: ['latin'],
  variable: '--font-montserrat',
  display: 'swap',
});

const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap',
});

interface LayoutProps {
  children: ReactNode;
  className?: string;
}

export function Layout({ children, className }: LayoutProps) {
  return (
    <>
      <Head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta httpEquiv="X-UA-Compatible" content="ie=edge" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </Head>
      
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem={false}
        disableTransitionOnChange
      >
        <div className={cn(
          'min-h-screen bg-off-white text-charcoal',
          'font-sans antialiased',
          montserrat.variable,
          playfair.variable,
          className
        )}>
          {children}
        </div>
      </ThemeProvider>
    </>
  );
}
