'use client';

import { useEffect } from 'react';

export default function Home() {
  useEffect(() => {
    // Mobile menu toggle
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');

    if (menuToggle && mobileMenu) {
      menuToggle.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Testimonial slider
    let currentTestimonial = 0;
    const testimonials = document.querySelectorAll('#testimonial-slider > div');
    const dots = document.querySelectorAll('.testimonial-dot');

    const showTestimonial = (index: number) => {
      const slider = document.getElementById('testimonial-slider');
      if (slider) {
        slider.style.transform = `translateX(-${index * 100}%)`;
      }

      dots.forEach((dot, i) => {
        if (i === index) {
          dot.classList.add('bg-deep-teal');
          dot.classList.remove('bg-deep-teal', 'bg-opacity-30');
        } else {
          dot.classList.remove('bg-deep-teal');
          dot.classList.add('bg-deep-teal', 'bg-opacity-30');
        }
      });
    };

    // Auto-advance testimonials
    const testimonialInterval = setInterval(() => {
      currentTestimonial = (currentTestimonial + 1) % testimonials.length;
      showTestimonial(currentTestimonial);
    }, 5000);

    // Navigation buttons
    const prevBtn = document.getElementById('prev-testimonial');
    const nextBtn = document.getElementById('next-testimonial');

    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
        currentTestimonial = currentTestimonial === 0 ? testimonials.length - 1 : currentTestimonial - 1;
        showTestimonial(currentTestimonial);
      });
    }

    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
        currentTestimonial = (currentTestimonial + 1) % testimonials.length;
        showTestimonial(currentTestimonial);
      });
    }

    // Dot navigation
    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => {
        currentTestimonial = index;
        showTestimonial(currentTestimonial);
      });
    });

    return () => {
      clearInterval(testimonialInterval);
    };
  }, []);

  return (
    <div className="font-sans text-gray-800 bg-white">
      {/* Navigation */}
      <nav className="fixed w-full bg-white bg-opacity-95 shadow-md z-50 transition-all duration-300" id="navbar">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-deep-teal font-serif font-bold text-xl">EmBrace Holdings</span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#home" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</a>
              <a href="#about" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">About</a>
              <a href="#services" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</a>
              <a href="#impact" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Impact</a>
              <a href="#contact" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Contact</a>
              <button className="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                Investor Portal
              </button>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button id="menu-toggle" className="text-deep-teal focus:outline-none">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          <div id="mobile-menu" className="md:hidden hidden pb-4">
            <div className="flex flex-col space-y-4">
              <a href="#home" className="text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</a>
              <a href="#about" className="text-gray-700 hover:text-deep-teal font-medium transition-colors">About</a>
              <a href="#services" className="text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</a>
              <a href="#impact" className="text-gray-700 hover:text-deep-teal font-medium transition-colors">Impact</a>
              <a href="#contact" className="text-gray-700 hover:text-deep-teal font-medium transition-colors">Contact</a>
              <button className="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all w-full">
                Investor Portal
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="hero-pattern min-h-screen flex items-center pt-20">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-white">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
                <span className="block">Global Vision.</span>
                <span className="block">Humanitarian Impact.</span>
                <span className="block">Financial Excellence.</span>
              </h1>
              <div className="h-1 w-24 gold-gradient mb-6"></div>
              <p className="text-lg md:text-xl opacity-90 mb-8 max-w-lg">
                EmBrace Holdings Enterprise LLC drives economic development through strategic investments and humanitarian initiatives across the globe.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <button className="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-xl transition-all">
                  Our Services
                </button>
                <button className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-full font-medium hover:bg-white hover:text-deep-teal transition-all">
                  Learn More
                </button>
              </div>
            </div>
            <div className="relative flex justify-center">
              <div className="relative animate-float">
                <svg className="w-64 h-64 md:w-80 md:h-80 text-white opacity-90 globe-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="0.5" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M2 12h20"></path>
                  <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-20 h-20 gold-gradient rounded-full flex items-center justify-center">
                    <span className="text-midnight-black font-serif font-bold text-2xl">EH</span>
                  </div>
                </div>
                <div className="globe-bg"></div>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full">
            <path fill="#ffffff" fillOpacity="1" d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
          </svg>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">About EmBrace Holdings</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              A global leader in asset management, private equity, and project development with a humanitarian mission at our core.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="order-2 lg:order-1">
              <h3 className="text-2xl font-serif font-bold text-deep-teal mb-6">Our Vision & Mission</h3>
              <p className="text-gray-600 mb-6">
                EmBrace Holdings Enterprise LLC was founded on the principle that financial success and humanitarian impact can go hand in hand. We believe in creating sustainable economic growth while addressing global challenges.
              </p>
              <p className="text-gray-600 mb-8">
                Our team of experts brings decades of experience in finance, development, and humanitarian work to create innovative solutions that benefit communities worldwide while delivering exceptional returns for our investors.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-lg border-l-4 border-deep-teal">
                  <div className="text-deep-teal mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h4 className="font-bold text-lg mb-2">Our Values</h4>
                  <p className="text-gray-600">Integrity, innovation, and impact guide every decision we make.</p>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg border-l-4 border-royal-gold">
                  <div className="text-royal-gold mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h4 className="font-bold text-lg mb-2">Our Approach</h4>
                  <p className="text-gray-600">Strategic investments with measurable social and financial returns.</p>
                </div>
              </div>
            </div>

            <div className="order-1 lg:order-2 relative">
              <div className="relative z-10 rounded-lg overflow-hidden shadow-xl">
                <div className="aspect-w-16 aspect-h-9 bg-deep-teal">
                  <svg className="w-full h-full text-white opacity-10" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                  </svg>
                </div>
                <div className="absolute inset-0 flex flex-col justify-center items-center text-white p-8">
                  <h3 className="text-2xl md:text-3xl font-serif font-bold mb-4 text-center">Global Presence</h3>
                  <p className="text-center mb-6">Operating in 30+ countries across 5 continents</p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                    <div className="text-center">
                      <div className="text-royal-gold text-3xl font-bold">$2.7B</div>
                      <div className="text-sm">Assets Under Management</div>
                    </div>
                    <div className="text-center">
                      <div className="text-royal-gold text-3xl font-bold">120+</div>
                      <div className="text-sm">Projects Funded</div>
                    </div>
                    <div className="text-center">
                      <div className="text-royal-gold text-3xl font-bold">15+</div>
                      <div className="text-sm">Years Experience</div>
                    </div>
                    <div className="text-center">
                      <div className="text-royal-gold text-3xl font-bold">5M+</div>
                      <div className="text-sm">Lives Impacted</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="absolute -bottom-6 -right-6 w-64 h-64 bg-royal-gold opacity-10 rounded-full"></div>
              <div className="absolute -top-6 -left-6 w-32 h-32 bg-deep-teal opacity-10 rounded-full"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Our Services</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Comprehensive financial solutions designed to create lasting value and positive global impact.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Asset Management */}
            <div className="service-card bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-2 gold-gradient"></div>
              <div className="p-6">
                <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold text-deep-teal mb-3">Asset Management</h3>
                <p className="text-gray-600 mb-6">
                  Strategic portfolio management with a focus on sustainable, long-term growth and social impact.
                </p>
                <ul className="text-gray-600 mb-6 space-y-2">
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    ESG-Focused Investments
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Wealth Preservation
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Risk Management
                  </li>
                </ul>
                <a href="#" className="text-deep-teal font-medium hover:text-royal-gold transition-colors flex items-center">
                  Learn More
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Private Equity */}
            <div className="service-card bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-2 gold-gradient"></div>
              <div className="p-6">
                <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold text-deep-teal mb-3">Private Equity</h3>
                <p className="text-gray-600 mb-6">
                  Strategic investments in high-potential companies that drive innovation and social change.
                </p>
                <ul className="text-gray-600 mb-6 space-y-2">
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Growth Capital
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Buyouts & Acquisitions
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Venture Capital
                  </li>
                </ul>
                <a href="#" className="text-deep-teal font-medium hover:text-royal-gold transition-colors flex items-center">
                  Learn More
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Project Development */}
            <div className="service-card bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-2 gold-gradient"></div>
              <div className="p-6">
                <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold text-deep-teal mb-3">Project Development</h3>
                <p className="text-gray-600 mb-6">
                  End-to-end management of complex projects that address critical global challenges.
                </p>
                <ul className="text-gray-600 mb-6 space-y-2">
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Infrastructure Development
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Sustainable Energy
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Healthcare Facilities
                  </li>
                </ul>
                <a href="#" className="text-deep-teal font-medium hover:text-royal-gold transition-colors flex items-center">
                  Learn More
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Finance */}
            <div className="service-card bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-2 gold-gradient"></div>
              <div className="p-6">
                <div className="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold text-deep-teal mb-3">Finance</h3>
                <p className="text-gray-600 mb-6">
                  Innovative financial solutions that enable growth and create positive social impact.
                </p>
                <ul className="text-gray-600 mb-6 space-y-2">
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Project Financing
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Structured Finance
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                    </svg>
                    Impact Investing
                  </li>
                </ul>
                <a href="#" className="text-deep-teal font-medium hover:text-royal-gold transition-colors flex items-center">
                  Learn More
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Section */}
      <section id="impact" className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Our Global Impact</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Creating sustainable change through strategic investments and humanitarian initiatives.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="order-2 lg:order-1">
              <div className="mb-8">
                <h3 className="text-2xl font-serif font-bold text-deep-teal mb-4">Humanitarian Focus</h3>
                <p className="text-gray-600 mb-4">
                  At EmBrace Holdings, we believe that financial success and humanitarian impact go hand in hand. Our projects are designed to address critical global challenges while delivering exceptional returns.
                </p>
                <p className="text-gray-600">
                  From sustainable infrastructure to healthcare facilities, our investments create lasting positive change in communities worldwide.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <h4 className="font-bold text-lg">Clean Energy</h4>
                  </div>
                  <p className="text-gray-600">
                    Financing renewable energy projects that provide clean power to underserved communities.
                  </p>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                      </svg>
                    </div>
                    <h4 className="font-bold text-lg">Infrastructure</h4>
                  </div>
                  <p className="text-gray-600">
                    Building critical infrastructure that connects communities and enables economic growth.
                  </p>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <h4 className="font-bold text-lg">Healthcare</h4>
                  </div>
                  <p className="text-gray-600">
                    Investing in healthcare facilities and technologies that improve access to quality care.
                  </p>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <h4 className="font-bold text-lg">Education</h4>
                  </div>
                  <p className="text-gray-600">
                    Supporting educational initiatives that empower communities through knowledge and skills.
                  </p>
                </div>
              </div>

              <button className="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-xl transition-all">
                View Impact Report
              </button>
            </div>

            <div className="order-1 lg:order-2">
              <div className="relative">
                <div className="bg-deep-teal rounded-lg p-8 text-white relative z-10">
                  <h3 className="text-2xl font-serif font-bold mb-6">Impact Metrics</h3>

                  <div className="space-y-6">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Clean Water Access</span>
                        <span>85%</span>
                      </div>
                      <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                        <div className="gold-gradient h-2 rounded-full" style={{width: '85%'}}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Renewable Energy</span>
                        <span>72%</span>
                      </div>
                      <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                        <div className="gold-gradient h-2 rounded-full" style={{width: '72%'}}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Healthcare Access</span>
                        <span>68%</span>
                      </div>
                      <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                        <div className="gold-gradient h-2 rounded-full" style={{width: '68%'}}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Education Initiatives</span>
                        <span>91%</span>
                      </div>
                      <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                        <div className="gold-gradient h-2 rounded-full" style={{width: '91%'}}></div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8 pt-6 border-t border-white border-opacity-20">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-royal-gold text-3xl font-bold">$420M</div>
                        <div className="text-sm text-white text-opacity-80">Impact Investments</div>
                      </div>
                      <div>
                        <div className="text-royal-gold text-3xl font-bold">27</div>
                        <div className="text-sm text-white text-opacity-80">Countries Served</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="absolute top-4 right-4 -z-10 w-full h-full bg-royal-gold opacity-20 rounded-lg transform rotate-3"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">What Our Partners Say</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Hear from organizations and communities that have partnered with EmBrace Holdings.
            </p>
          </div>

          <div className="relative">
            <div className="overflow-hidden">
              <div className="flex testimonial-slider transition-transform duration-500 ease-in-out" id="testimonial-slider">
                {/* Testimonial 1 */}
                <div className="min-w-full px-4">
                  <div className="bg-white rounded-lg shadow-lg p-8 relative">
                    <div className="text-royal-gold mb-6">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 opacity-20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                      </svg>
                    </div>
                    <p className="text-gray-600 text-lg mb-6">
                      "EmBrace Holdings' investment in our renewable energy project has transformed our community. Not only did they provide the capital we needed, but their expertise and guidance were invaluable throughout the process."
                    </p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full bg-deep-teal flex items-center justify-center text-white font-bold text-xl mr-4">
                        SE
                      </div>
                      <div>
                        <h4 className="font-bold text-deep-teal">Sarah Edwards</h4>
                        <p className="text-gray-500">CEO, GreenPower Initiatives</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Testimonial 2 */}
                <div className="min-w-full px-4">
                  <div className="bg-white rounded-lg shadow-lg p-8 relative">
                    <div className="text-royal-gold mb-6">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 opacity-20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                      </svg>
                    </div>
                    <p className="text-gray-600 text-lg mb-6">
                      "The team at EmBrace Holdings understands that true impact requires both financial acumen and a deep commitment to humanitarian values. Their approach to our healthcare infrastructure project was comprehensive and visionary."
                    </p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full bg-deep-teal flex items-center justify-center text-white font-bold text-xl mr-4">
                        MJ
                      </div>
                      <div>
                        <h4 className="font-bold text-deep-teal">Dr. Michael Johnson</h4>
                        <p className="text-gray-500">Director, Global Health Partners</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Testimonial 3 */}
                <div className="min-w-full px-4">
                  <div className="bg-white rounded-lg shadow-lg p-8 relative">
                    <div className="text-royal-gold mb-6">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 opacity-20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                      </svg>
                    </div>
                    <p className="text-gray-600 text-lg mb-6">
                      "Working with EmBrace Holdings has been transformative for our educational initiative. Their strategic approach to financing and development has allowed us to expand our reach and impact in ways we never thought possible."
                    </p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full bg-deep-teal flex items-center justify-center text-white font-bold text-xl mr-4">
                        AL
                      </div>
                      <div>
                        <h4 className="font-bold text-deep-teal">Amara Lawal</h4>
                        <p className="text-gray-500">Founder, Education Without Borders</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-center mt-8 space-x-2">
              <button className="w-3 h-3 rounded-full bg-deep-teal testimonial-dot" data-index="0"></button>
              <button className="w-3 h-3 rounded-full bg-deep-teal bg-opacity-30 testimonial-dot" data-index="1"></button>
              <button className="w-3 h-3 rounded-full bg-deep-teal bg-opacity-30 testimonial-dot" data-index="2"></button>
            </div>

            <button id="prev-testimonial" className="absolute top-1/2 left-0 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-lg focus:outline-none">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button id="next-testimonial" className="absolute top-1/2 right-0 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-lg focus:outline-none">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Get in Touch</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Connect with our team to explore partnership opportunities and learn more about our services.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <div className="bg-gray-50 rounded-lg p-8 h-full">
                <h3 className="text-2xl font-serif font-bold text-deep-teal mb-6">Contact Information</h3>

                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">Global Headquarters</h4>
                      <p className="text-gray-600">123 Financial District<br />New York, NY 10004<br />United States</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">Phone</h4>
                      <p className="text-gray-600">+****************<br />+****************</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">Email</h4>
                      <p className="text-gray-600"><EMAIL><br /><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">Business Hours</h4>
                      <p className="text-gray-600">Monday - Friday: 9:00 AM - 6:00 PM EST<br />Saturday: 10:00 AM - 2:00 PM EST</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="font-bold text-lg mb-4">Follow Us</h4>
                  <div className="flex space-x-4">
                    <a href="#" className="w-10 h-10 rounded-full bg-deep-teal text-white flex items-center justify-center hover:bg-royal-gold transition-colors">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                      </svg>
                    </a>
                    <a href="#" className="w-10 h-10 rounded-full bg-deep-teal text-white flex items-center justify-center hover:bg-royal-gold transition-colors">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                    <a href="#" className="w-10 h-10 rounded-full bg-deep-teal text-white flex items-center justify-center hover:bg-royal-gold transition-colors">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <form className="bg-white border border-gray-200 rounded-lg p-8 shadow-lg">
                <h3 className="text-2xl font-serif font-bold text-deep-teal mb-6">Send us a Message</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <input type="text" id="firstName" name="firstName" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="John" />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <input type="text" id="lastName" name="lastName" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="Doe" />
                  </div>
                </div>

                <div className="mb-6">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                  <input type="email" id="email" name="email" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="<EMAIL>" />
                </div>

                <div className="mb-6">
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">Company/Organization</label>
                  <input type="text" id="company" name="company" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="Your Company" />
                </div>

                <div className="mb-6">
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                  <select id="subject" name="subject" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors">
                    <option value="">Select a subject</option>
                    <option value="investment">Investment Opportunities</option>
                    <option value="partnership">Partnership Inquiry</option>
                    <option value="consultation">Consultation Request</option>
                    <option value="media">Media Inquiry</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="mb-6">
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                  <textarea id="message" name="message" rows={5} className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors resize-vertical" placeholder="Tell us about your project or inquiry..."></textarea>
                </div>

                <button type="submit" className="w-full gold-gradient text-midnight-black font-bold py-3 px-6 rounded-lg hover:shadow-lg transition-all">
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-midnight-black text-white py-16">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div>
              <div className="flex items-center space-x-2 mb-6">
                <div className="relative">
                  <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                    <span className="text-white font-serif font-bold text-xl">E</span>
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                    <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                  </div>
                </div>
                <span className="text-white font-serif font-bold text-xl">EmBrace Holdings</span>
              </div>
              <p className="text-gray-300 mb-6">
                Driving economic development through strategic investments and humanitarian initiatives across the globe.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 rounded-full bg-deep-teal text-white flex items-center justify-center hover:bg-royal-gold transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-deep-teal text-white flex items-center justify-center hover:bg-royal-gold transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-deep-teal text-white flex items-center justify-center hover:bg-royal-gold transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                  </svg>
                </a>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-serif font-bold mb-6">Services</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Asset Management</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Private Equity</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Project Development</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Financial Advisory</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Impact Investing</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-serif font-bold mb-6">Company</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">About Us</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Our Team</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Careers</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">News & Insights</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Contact</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-serif font-bold mb-6">Resources</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Investor Portal</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Impact Reports</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Research</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">ESG Framework</a></li>
                <li><a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Privacy Policy</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-300 mb-4 md:mb-0">
                © 2024 EmBrace Holdings Enterprise LLC. All rights reserved.
              </p>
              <div className="flex space-x-6">
                <a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Terms of Service</a>
                <a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Privacy Policy</a>
                <a href="#" className="text-gray-300 hover:text-royal-gold transition-colors">Cookie Policy</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
