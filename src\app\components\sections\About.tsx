import Image from 'next/image';

const features = [
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
      </svg>
    ),
    title: 'Global Reach',
    description: 'Operating across continents to deliver impact where it\'s needed most.'
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>
    ),
    title: 'Humanitarian Focus',
    description: 'Putting people first in all our investment and development initiatives.'
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
    title: 'Innovation',
    description: 'Leveraging cutting-edge solutions to solve complex challenges.'
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    title: 'Sustainable Growth',
    description: 'Balancing financial returns with environmental and social responsibility.'
  }
];

const About = () => {
  return (
    <section id="about" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-playfair font-bold mb-6">About EmBrace Holdings</h2>
          <div className="w-24 h-1 bg-gradient-to-r from-royal-gold to-amber-300 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Founded with a vision to create lasting impact, EmBrace Holdings has been at the forefront of sustainable development and strategic investments for over two decades.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          <div className="relative">
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <Image 
                src="/images/about-image.jpg" 
                alt="Team meeting"
                width={800}
                height={600}
                className="w-full h-auto"
              />
            </div>
            <div className="absolute -bottom-6 -right-6 bg-royal-gold p-6 rounded-lg shadow-lg w-3/4">
              <h3 className="text-xl font-bold text-white mb-2">Our Mission</h3>
              <p className="text-white">To create lasting value through innovative and sustainable investments</p>
            </div>
          </div>

          <div>
            <h3 className="text-3xl font-bold text-gray-800 mb-6">Driving Sustainable Growth in Africa</h3>
            <p className="text-gray-600 mb-6">
              EmBrace Holdings is a leading investment company with a focus on sustainable development across the African continent. 
              With over two decades of experience, we specialize in identifying and capitalizing on high-impact opportunities 
              that deliver both financial returns and positive social impact.
            </p>
            <p className="text-gray-600 mb-8">
              Our diverse portfolio spans real estate development, renewable energy, agriculture, and infrastructure projects 
              that are transforming communities and creating lasting value.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="bg-deep-teal/10 p-3 rounded-full text-deep-teal">
                    {feature.icon}
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-800">{feature.title}</h4>
                    <p className="text-gray-600 text-sm">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
