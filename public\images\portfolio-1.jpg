<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="portfolioGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#005A5B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#007D7E;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#portfolioGrad1)"/>
  
  <!-- Solar panels representation -->
  <g fill="#D4AF37" opacity="0.8">
    <rect x="50" y="100" width="80" height="40" rx="4"/>
    <rect x="150" y="100" width="80" height="40" rx="4"/>
    <rect x="250" y="100" width="80" height="40" rx="4"/>
    
    <rect x="100" y="160" width="80" height="40" rx="4"/>
    <rect x="200" y="160" width="80" height="40" rx="4"/>
  </g>
  
  <!-- Grid lines on panels -->
  <g stroke="#005A5B" stroke-width="1" opacity="0.6">
    <line x1="70" y1="100" x2="70" y2="140"/>
    <line x1="90" y1="100" x2="90" y2="140"/>
    <line x1="110" y1="100" x2="110" y2="140"/>
    <line x1="50" y1="120" x2="130" y2="120"/>
    
    <line x1="170" y1="100" x2="170" y2="140"/>
    <line x1="190" y1="100" x2="190" y2="140"/>
    <line x1="210" y1="100" x2="210" y2="140"/>
    <line x1="150" y1="120" x2="230" y2="120"/>
  </g>
  
  <!-- Sun -->
  <circle cx="350" cy="50" r="25" fill="#D4AF37"/>
  <g stroke="#D4AF37" stroke-width="2">
    <line x1="350" y1="15" x2="350" y2="5"/>
    <line x1="385" y1="50" x2="395" y2="50"/>
    <line x1="350" y1="85" x2="350" y2="95"/>
    <line x1="315" y1="50" x2="305" y2="50"/>
    <line x1="372" y1="28" x2="379" y2="21"/>
    <line x1="372" y1="72" x2="379" y2="79"/>
    <line x1="328" y1="72" x2="321" y2="79"/>
    <line x1="328" y1="28" x2="321" y2="21"/>
  </g>
  
  <!-- Title overlay -->
  <rect x="0" y="250" width="400" height="50" fill="#000000" fill-opacity="0.7"/>
  <text x="200" y="275" text-anchor="middle" font-family="sans-serif" font-size="18" font-weight="bold" fill="#ffffff">Renewable Energy</text>
</svg>
