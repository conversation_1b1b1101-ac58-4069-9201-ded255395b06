{"css.customData": [".vscode/css_custom_data.json"], "editor.quickSuggestions": {"strings": true}, "tailwindCSS.includeLanguages": {"plaintext": "html"}, "tailwindCSS.experimental.classRegex": [["class:\\s*\"([^\"]*)\"", "[\\w\\d-:/.]+(?<!:)"], ["class:\\s*'([^']*)'", "[\\w\\d-:/.]+(?<!:)"], ["class:\\s*`([^`]*)`", "[\\w\\d-:/.]+(?<!:)"], ["class=\"([^\"]*)\"", "[\\w\\d-:/.]+(?<!:)"], ["class='([^']*)'", "[\\w\\d-:/.]+(?<!:)"], ["class=`([^`]*)`", "[\\w\\d-:/.]+(?<!:)"], ["classList\\.(\\w+)", "[\\w-]+(?<!:)"], ["\\bclassList\\.(\\w+)", "[\\w-]+(?<!:)"], ["tw`([^`]*)`", "[^`]*"]], "tailwindCSS.emmetCompletions": true, "editor.suggest.showWords": false, "editor.suggest.showKeywords": false, "editor.suggest.showSnippets": false, "editor.suggest.showClasses": false, "editor.suggest.showColors": false, "editor.suggest.showConstants": false, "editor.suggest.showEnums": false, "editor.suggest.showFields": false, "editor.suggest.showFiles": false, "editor.suggest.showFolders": false, "editor.suggest.showFunctions": false, "editor.suggest.showInterfaces": false, "editor.suggest.showIssues": false, "editor.suggest.showMethods": false, "editor.suggest.showModules": false, "editor.suggest.showOperators": false, "editor.suggest.showProperties": false, "editor.suggest.showReferences": false, "editor.suggest.showStructs": false, "editor.suggest.showTypeParameters": false, "editor.suggest.showUnits": false, "editor.suggest.showUsers": false, "editor.suggest.showValues": false, "editor.suggest.showVariables": false, "css.validate": false, "less.validate": false, "scss.validate": false, "tailwindCSS.validate": true, "files.associations": {"*.css": "tailwindcss"}}