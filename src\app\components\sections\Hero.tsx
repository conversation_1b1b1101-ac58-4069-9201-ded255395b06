import Image from 'next/image';
import Link from 'next/link';

const stats = [
  { number: '25+', label: 'Years Experience' },
  { number: '100+', label: 'Projects Completed' },
  { number: '1M+', label: 'Lives Impacted' },
];

const Hero = () => {
  const scrollTo = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    e.preventDefault();
    const element = document.getElementById(targetId.replace('#', ''));
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center bg-pattern bg-cover bg-center pt-20">
      <div className="absolute inset-0 bg-gradient-to-b from-deep-teal/95 to-midnight-black/95"></div>
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-playfair font-bold mb-6 leading-tight">
              Building a Better Future Through <span className="text-royal-gold">Innovation</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 mb-10 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
              We are committed to transforming communities through sustainable development and strategic investments that create lasting impact.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start">
              <a 
                href="#contact"
                onClick={(e) => scrollTo(e, '#contact')}
                className="bg-gradient-to-r from-royal-gold to-amber-300 text-midnight-black font-bold px-8 py-4 rounded-full hover:shadow-lg transition-all hover:scale-105 text-lg text-center"
              >
                Get Started
              </a>
              <a 
                href="#about"
                onClick={(e) => scrollTo(e, '#about')}
                className="border-2 border-white/30 text-white px-8 py-4 rounded-full hover:bg-white/10 transition-all text-lg font-medium text-center"
              >
                Learn More
              </a>
            </div>
            
            <div className="mt-16 flex flex-wrap justify-center lg:justify-start gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl font-bold text-royal-gold mb-1">{stat.number}</div>
                  <div className="text-gray-300">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="relative hidden lg:block">
            <div className="relative">
              <div className="absolute -top-6 -right-6 w-64 h-64 bg-royal-gold/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
              <div className="absolute -bottom-8 -left-8 w-72 h-72 bg-deep-teal/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
              <div className="relative rounded-2xl overflow-hidden shadow-2xl border-4 border-white/10">
                <Image 
                  src="/images/hero-image.jpg" 
                  alt="Modern building"
                  width={600}
                  height={800}
                  className="w-full h-auto"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
