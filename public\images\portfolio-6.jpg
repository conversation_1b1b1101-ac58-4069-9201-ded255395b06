<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="portfolioGrad6" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#007D7E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#005A5B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#portfolioGrad6)"/>
  
  <!-- Housing development -->
  <g fill="#ffffff" fill-opacity="0.9">
    <rect x="50" y="150" width="60" height="50" rx="4"/>
    <rect x="130" y="150" width="60" height="50" rx="4"/>
    <rect x="210" y="150" width="60" height="50" rx="4"/>
    <rect x="290" y="150" width="60" height="50" rx="4"/>
  </g>
  
  <!-- Roofs -->
  <g fill="#005A5B">
    <polygon points="50,150 80,130 110,150"/>
    <polygon points="130,150 160,130 190,150"/>
    <polygon points="210,150 240,130 270,150"/>
    <polygon points="290,150 320,130 350,150"/>
  </g>
  
  <!-- Doors -->
  <g fill="#007D7E">
    <rect x="75" y="180" width="10" height="20"/>
    <rect x="155" y="180" width="10" height="20"/>
    <rect x="235" y="180" width="10" height="20"/>
    <rect x="315" y="180" width="10" height="20"/>
  </g>
  
  <!-- Windows -->
  <g fill="#D4AF37" opacity="0.8">
    <rect x="60" y="165" width="8" height="10"/>
    <rect x="92" y="165" width="8" height="10"/>
    <rect x="140" y="165" width="8" height="10"/>
    <rect x="172" y="165" width="8" height="10"/>
    <rect x="220" y="165" width="8" height="10"/>
    <rect x="252" y="165" width="8" height="10"/>
    <rect x="300" y="165" width="8" height="10"/>
    <rect x="332" y="165" width="8" height="10"/>
  </g>
  
  <!-- Title overlay -->
  <rect x="0" y="250" width="400" height="50" fill="#000000" fill-opacity="0.7"/>
  <text x="200" y="275" text-anchor="middle" font-family="sans-serif" font-size="18" font-weight="bold" fill="#ffffff">Affordable Housing</text>
</svg>
