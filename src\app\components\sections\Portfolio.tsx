import Image from 'next/image';

const projects = [
  {
    title: 'Luxury Residential Complex',
    location: 'Nairobi, Kenya',
    category: 'Real Estate',
    image: '/images/portfolio-1.jpg',
    description: 'A sustainable luxury residential development featuring modern amenities and green spaces.'
  },
  {
    title: 'Solar Farm Initiative',
    location: 'Kano, Nigeria',
    category: 'Renewable Energy',
    image: '/images/portfolio-2.jpg',
    description: 'A 50MW solar farm providing clean energy to over 100,000 homes.'
  },
  {
    title: 'Urban Regeneration Project',
    location: 'Accra, Ghana',
    category: 'Infrastructure',
    image: '/images/portfolio-3.jpg',
    description: 'Revitalizing urban spaces to create sustainable communities.'
  },
  {
    title: 'Agricultural Hub',
    location: 'Arusha, Tanzania',
    category: 'Agriculture',
    image: '/images/portfolio-4.jpg',
    description: 'Modern farming techniques to boost local food production.'
  },
  {
    title: 'Tech Innovation Center',
    location: 'Kigali, Rwanda',
    category: 'Technology',
    image: '/images/portfolio-5.jpg',
    description: 'A hub for tech startups and innovation in East Africa.'
  },
  {
    title: 'Hospital Development',
    location: 'Lagos, Nigeria',
    category: 'Healthcare',
    image: '/images/portfolio-6.jpg',
    description: 'State-of-the-art healthcare facility serving the local community.'
  }
];

const Portfolio = () => {
  return (
    <section id="portfolio" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-playfair font-bold mb-6">Our Portfolio</h2>
          <div className="w-24 h-1 bg-gradient-to-r from-royal-gold to-amber-300 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our diverse range of projects that are making a real difference in communities across Africa.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <div key={index} className="group bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300">
              <div className="relative h-64 overflow-hidden">
                <Image 
                  src={project.image} 
                  alt={project.title}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-6">
                  <div>
                    <span className="inline-block px-3 py-1 text-sm font-medium text-white bg-royal-gold rounded-full mb-2">
                      {project.category}
                    </span>
                    <h3 className="text-xl font-bold text-white">{project.title}</h3>
                    <p className="text-gray-200">{project.location}</p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2">{project.title}</h3>
                <p className="text-gray-600 mb-4">{project.description}</p>
                <div className="flex items-center text-deep-teal font-medium">
                  View Project
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="bg-gradient-to-r from-deep-teal to-teal-600 text-white font-bold px-8 py-4 rounded-full hover:shadow-lg transition-all hover:scale-105">
            View All Projects
          </button>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
