'use client';

import { useEffect, useState } from 'react';
import Head from 'next/head';
import Script from 'next/script';
import Link from 'next/link';

export default function ExactOriginal() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeSlide, setActiveSlide] = useState(0);

  // Auto-rotate testimonials
  useEffect(() => {
    const timer = setInterval(() => {
      setActiveSlide((prev) => (prev + 1) % 3);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Head>
        <title>EmBrace Holdings - Your Trusted Partner in Real Estate</title>
        <meta name="description" content="EmBrace Holdings - Your Trusted Partner in Real Estate" />
      </Head>

      {/* Navigation */}
      <nav className="bg-white shadow-md fixed w-full z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-20">
            <div className="flex-shrink-0 flex items-center">
              <img className="h-12 w-auto" src="/images/logo.png" alt="EmBrace Holdings Logo" />
            </div>
            
            {/* Desktop Menu */}
            <div className="hidden md:ml-6 md:flex md:space-x-8">
              <Link href="#" className="border-transparent text-gray-900 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                Home
              </Link>
              <Link href="#about" className="border-transparent text-gray-900 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                About Us
              </Link>
              <Link href="#services" className="border-transparent text-gray-900 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                Services
              </Link>
              <Link href="#portfolio" className="border-transparent text-gray-900 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                Portfolio
              </Link>
              <Link href="#contact" className="border-transparent text-gray-900 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                Contact
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="-mr-2 flex items-center md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-900 hover:text-gray-700 focus:outline-none"
              >
                <span className="sr-only">Open main menu</span>
                <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="pt-2 pb-3 space-y-1">
              <Link href="#" className="bg-gray-50 border-teal-500 text-teal-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                Home
              </Link>
              <Link href="#about" className="border-transparent text-gray-900 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                About Us
              </Link>
              <Link href="#services" className="border-transparent text-gray-900 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                Services
              </Link>
              <Link href="#portfolio" className="border-transparent text-gray-900 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                Portfolio
              </Link>
              <Link href="#contact" className="border-transparent text-gray-900 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                Contact
              </Link>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <div className="relative bg-gray-900 pt-32 pb-24 sm:pt-40 sm:pb-32 lg:pt-48 lg:pb-40">
        <div className="absolute inset-0 overflow-hidden">
          <img
            className="w-full h-full object-cover opacity-30"
            src="/images/hero-bg.jpg"
            alt=""
          />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
              <span className="block">Your Trusted Partner in</span>
              <span className="block text-teal-400">Real Estate Excellence</span>
            </h1>
            <p className="mt-3 max-w-md mx-auto text-base text-gray-300 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
              Transforming visions into valuable real estate assets through innovative investment strategies and expert property management.
            </p>
            <div className="mt-10 flex justify-center">
              <div className="inline-flex rounded-md shadow">
                <Link
                  href="#contact"
                  className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700"
                >
                  Get Started
                </Link>
              </div>
              <div className="ml-3 inline-flex">
                <Link
                  href="#about"
                  className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-teal-300 bg-teal-900 bg-opacity-40 hover:bg-opacity-60"
                >
                  Learn More
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:py-16 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            <div className="text-center">
              <p className="text-4xl font-extrabold text-teal-600 sm:text-5xl">$1.2B+</p>
              <p className="mt-2 text-sm font-medium text-gray-500">Assets Under Management</p>
            </div>
            <div className="text-center">
              <p className="text-4xl font-extrabold text-teal-600 sm:text-5xl">25+</p>
              <p className="mt-2 text-sm font-medium text-gray-500">Years Experience</p>
            </div>
            <div className="text-center">
              <p className="text-4xl font-extrabold text-teal-600 sm:text-5xl">500+</p>
              <p className="mt-2 text-sm font-medium text-gray-500">Properties Acquired</p>
            </div>
            <div className="text-center">
              <p className="text-4xl font-extrabold text-teal-600 sm:text-5xl">98%</p>
              <p className="mt-2 text-sm font-medium text-gray-500">Client Satisfaction</p>
            </div>
          </div>
        </div>
      </div>

      {/* About Preview Section */}
      <div id="about" className="bg-gray-50 py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-teal-600 font-semibold tracking-wide uppercase">About Us</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              A New Standard in Real Estate Investment
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              EmBrace Holdings combines deep market knowledge with innovative investment strategies to deliver exceptional returns.
            </p>
          </div>

          <div className="mt-20">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {[
                {
                  name: 'Strategic Acquisitions',
                  description: 'Identifying undervalued properties with high growth potential through comprehensive market analysis.',
                  icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
                },
                {
                  name: 'Value Enhancement',
                  description: 'Implementing strategic improvements to maximize property value and investment returns.',
                  icon: 'M13 10V3L4 14h7v7l9-11h-7z',
                },
                {
                  name: 'Portfolio Management',
                  description: 'Comprehensive oversight and optimization of your real estate investment portfolio.',
                  icon: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15',
                },
              ].map((feature) => (
                <div key={feature.name} className="pt-6">
                  <div className="flow-root bg-white rounded-lg px-6 pb-8">
                    <div className="-mt-6">
                      <div>
                        <span className="inline-flex items-center justify-center p-3 bg-teal-500 rounded-md shadow-lg">
                          <svg
                            className="h-6 w-6 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            aria-hidden="true"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d={feature.icon}
                            />
                          </svg>
                        </span>
                      </div>
                      <h3 className="mt-8 text-lg font-medium text-gray-900 tracking-tight">{feature.name}</h3>
                      <p className="mt-5 text-base text-gray-500">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="bg-white py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center mb-12">
            <h2 className="text-base text-teal-600 font-semibold tracking-wide uppercase">Testimonials</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              What Our Clients Say
            </p>
          </div>

          <div className="relative">
            <div className="relative overflow-hidden">
              <div className="relative">
                <div className="relative h-96">
                  {[
                    {
                      quote: "Working with EmBrace Holdings transformed our real estate portfolio. Their expertise and attention to detail are unmatched.",
                      author: "Sarah Johnson",
                      title: "CEO, Johnson Enterprises",
                    },
                    {
                      quote: "The team's strategic approach to property investment has consistently delivered above-market returns for our family office.",
                      author: "Michael Chen",
                      title: "Managing Director, Chen Capital",
                    },
                    {
                      quote: "Professional, transparent, and results-driven. EmBrace Holdings is our trusted partner for all real estate investments.",
                      author: "Robert Williams",
                      title: "CFO, Williams Group",
                    },
                  ].map((testimonial, index) => (
                    <div
                      key={index}
                      className={`absolute inset-0 transition-opacity duration-500 ${
                        activeSlide === index ? 'opacity-100' : 'opacity-0 pointer-events-none'
                      }`}
                    >
                      <div className="h-full flex items-center justify-center">
                        <div className="max-w-3xl mx-auto text-center px-4 sm:px-6 lg:px-8">
                          <div className="relative text-lg font-medium text-gray-900">
                            <svg
                              className="absolute top-0 left-0 transform -translate-x-8 -translate-y-8 h-8 w-8 text-gray-200"
                              fill="currentColor"
                              viewBox="0 0 32 32"
                              aria-hidden="true"
                            >
                              <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.016 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.016 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                            </svg>
                            <p className="relative text-xl">{testimonial.quote}</p>
                          </div>
                          <div className="mt-8">
                            <div className="text-base font-medium text-teal-600">{testimonial.author}</div>
                            <div className="text-base text-gray-500">{testimonial.title}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Slider Controls */}
            <div className="mt-10 flex justify-center space-x-3">
              {[0, 1, 2].map((index) => (
                <button
                  key={index}
                  onClick={() => setActiveSlide(index)}
                  className={`w-3 h-3 rounded-full ${
                    activeSlide === index ? 'bg-teal-600' : 'bg-gray-300'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-teal-700">
        <div className="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            <span className="block">Ready to grow your real estate portfolio?</span>
          </h2>
          <p className="mt-4 text-lg leading-6 text-teal-200">
            Contact us today to learn how we can help you achieve your investment goals.
          </p>
          <Link
            href="#contact"
            className="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-teal-600 bg-white hover:bg-teal-50 sm:w-auto"
          >
            Get in Touch
          </Link>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
          <div className="xl:grid xl:grid-cols-3 xl:gap-8">
            <div className="space-y-8 xl:col-span-1">
              <img className="h-10" src="/images/logo-white.png" alt="EmBrace Holdings" />
              <p className="text-gray-300 text-base">
                Your trusted partner in real estate investment and property management.
              </p>
              <div className="flex space-x-6">
                {['facebook', 'twitter', 'linkedin', 'instagram'].map((social) => (
                  <a key={social} href="#" className="text-gray-400 hover:text-gray-300">
                    <span className="sr-only">{social}</span>
                    <i className={`fab fa-${social} h-6 w-6`} />
                  </a>
                ))}
              </div>
            </div>
            <div className="mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2">
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">Company</h3>
                  <ul className="mt-4 space-y-4">
                    {['About', 'Team', 'Careers', 'Blog'].map((item) => (
                      <li key={item}>
                        <a href="#" className="text-base text-gray-400 hover:text-white">
                          {item}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-12 md:mt-0">
                  <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">Legal</h3>
                  <ul className="mt-4 space-y-4">
                    {['Privacy', 'Terms', 'Disclosure', 'Compliance'].map((item) => (
                      <li key={item}>
                        <a href="#" className="text-base text-gray-400 hover:text-white">
                          {item}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">Contact</h3>
                  <ul className="mt-4 space-y-4">
                    <li className="text-base text-gray-400">
                      123 Investment Ave, Suite 100<br />
                      New York, NY 10001
                    </li>
                    <li className="text-base text-gray-400">
                      <EMAIL><br />
                      +1 (555) 123-4567
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8">
            <p className="text-base text-gray-400 text-center">
              &copy; {new Date().getFullYear()} EmBrace Holdings. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
