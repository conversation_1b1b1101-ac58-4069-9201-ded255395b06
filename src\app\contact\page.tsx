'use client';

import Link from 'next/link';

export default function Contact() {
  return (
    <div className="font-sans text-gray-800 bg-white">
      {/* Navigation */}
      <nav className="fixed w-full bg-white bg-opacity-95 shadow-md z-50 transition-all duration-300">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-deep-teal font-serif font-bold text-xl">EmBrace Holdings</span>
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</Link>
              <Link href="/about" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">About</Link>
              <Link href="/services" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</Link>
              <Link href="/team" className="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Team</Link>
              <Link href="/contact" className="nav-link text-deep-teal font-medium">Contact</Link>
              <Link href="/investor-portal" className="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                Investor Portal
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero-pattern min-h-screen flex items-center pt-20">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 py-20">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
              Get in Touch
            </h1>
            <div className="h-1 w-24 gold-gradient mb-6 mx-auto"></div>
            <p className="text-lg md:text-xl opacity-90 mb-8 max-w-3xl mx-auto">
              Connect with our team to explore partnership opportunities and learn more about our services.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <div className="bg-gray-50 rounded-lg p-8 h-full">
                <h3 className="text-2xl font-serif font-bold text-deep-teal mb-6">Contact Information</h3>
                
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">Global Headquarters</h4>
                      <p className="text-gray-600">123 Financial District<br />New York, NY 10004<br />United States</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">Phone</h4>
                      <p className="text-gray-600">+****************<br />+****************</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">Email</h4>
                      <p className="text-gray-600"><EMAIL><br /><EMAIL></p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">Business Hours</h4>
                      <p className="text-gray-600">Monday - Friday: 9:00 AM - 6:00 PM EST<br />Saturday: 10:00 AM - 2:00 PM EST</p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="font-bold text-lg mb-4">Follow Us</h4>
                  <div className="flex space-x-4">
                    <a href="#" className="w-10 h-10 rounded-full bg-deep-teal text-white flex items-center justify-center hover:bg-royal-gold transition-colors">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                      </svg>
                    </a>
                    <a href="#" className="w-10 h-10 rounded-full bg-deep-teal text-white flex items-center justify-center hover:bg-royal-gold transition-colors">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <form className="bg-white border border-gray-200 rounded-lg p-8 shadow-lg">
                <h3 className="text-2xl font-serif font-bold text-deep-teal mb-6">Send us a Message</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <input type="text" id="firstName" name="firstName" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="John" />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <input type="text" id="lastName" name="lastName" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="Doe" />
                  </div>
                </div>
                
                <div className="mb-6">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                  <input type="email" id="email" name="email" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="<EMAIL>" />
                </div>
                
                <div className="mb-6">
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">Company/Organization</label>
                  <input type="text" id="company" name="company" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors" placeholder="Your Company" />
                </div>
                
                <div className="mb-6">
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                  <select id="subject" name="subject" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors">
                    <option value="">Select a subject</option>
                    <option value="investment">Investment Opportunities</option>
                    <option value="partnership">Partnership Inquiry</option>
                    <option value="consultation">Consultation Request</option>
                    <option value="media">Media Inquiry</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                
                <div className="mb-6">
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                  <textarea id="message" name="message" rows={5} className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-deep-teal focus:border-transparent transition-colors resize-vertical" placeholder="Tell us about your project or inquiry..."></textarea>
                </div>
                
                <button type="submit" className="w-full gold-gradient text-midnight-black font-bold py-3 px-6 rounded-lg hover:shadow-lg transition-all">
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Office Locations */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Global Offices</h2>
            <div className="section-divider mx-auto mb-6 w-24"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-6 shadow-lg text-center">
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-2">New York</h3>
              <p className="text-gray-600 mb-4">Global Headquarters</p>
              <p className="text-gray-600">123 Financial District<br />New York, NY 10004<br />United States</p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-lg text-center">
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-2">London</h3>
              <p className="text-gray-600 mb-4">European Operations</p>
              <p className="text-gray-600">45 Canary Wharf<br />London E14 5AB<br />United Kingdom</p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-lg text-center">
              <h3 className="text-xl font-serif font-bold text-deep-teal mb-2">Singapore</h3>
              <p className="text-gray-600 mb-4">Asia Pacific Hub</p>
              <p className="text-gray-600">Marina Bay Financial Centre<br />Singapore 018989<br />Singapore</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-midnight-black text-white py-16">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-2 mb-6">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                  <span className="text-white font-serif font-bold text-xl">E</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                  <span className="text-midnight-black font-serif font-bold text-xs">H</span>
                </div>
              </div>
              <span className="text-white font-serif font-bold text-xl">EmBrace Holdings</span>
            </Link>
            <p className="text-gray-300 mb-6">
              © 2024 EmBrace Holdings Enterprise LLC. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
