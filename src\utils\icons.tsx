import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faGlobeAmericas, 
  faHandHoldingHeart, 
  faChartLine,
  // Add other icons you need here
} from '@fortawesome/free-solid-svg-icons';
import { 
  faTwitter,
  faLinkedin,
  faFacebook,
  faInstagram,
} from '@fortawesome/free-brands-svg-icons';

// Map icon names to their corresponding Font Awesome icons
const iconMap: { [key: string]: any } = {
  // Solid Icons
  'globe-americas': faGlobeAmericas,
  'hand-holding-heart': faHandHoldingHeart,
  'chart-line': faChartLine,
  
  // Brand Icons
  'twitter': faTwitter,
  'linkedin': faLinkedin,
  'facebook': faFacebook,
  'instagram': faInstagram,
};

interface IconProps {
  name: string;
  className?: string;
  [key: string]: any;
}

export const Icon = ({ name, className = '', ...props }: IconProps) => {
  const icon = iconMap[name];
  
  if (!icon) {
    console.warn(`Icon '${name}' not found`);
    return null;
  }

  return (
    <FontAwesomeIcon 
      icon={icon} 
      className={className} 
      {...props} 
    />
  );
};
