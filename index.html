

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmBrace Holdings Enterprise LLC</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'deep-teal': '#005A5B',
                        'light-teal': '#007D7E',
                        'royal-gold': '#D4AF37',
                        'midnight-black': '#121212',
                    },
                    fontFamily: {
                        'sans': ['Montserrat', 'sans-serif'],
                        'serif': ['Playfair Display', 'serif'],
                    },
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        .gold-gradient {
            background: linear-gradient(135deg, #D4AF37 0%, #FFDF00 50%, #D4AF37 100%);
        }
        
        .hero-pattern {
            background-color: #005A5B;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
        
        .section-divider {
            height: 4px;
            background: linear-gradient(90deg, transparent, #D4AF37, transparent);
        }
        
        .nav-link {
            position: relative;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 0;
            background: linear-gradient(90deg, #D4AF37, #FFDF00);
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .service-card {
            transition: all 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .testimonial-slider {
            scroll-behavior: smooth;
        }
        
        .globe-icon {
            position: relative;
            z-index: 10;
        }
        
        .globe-bg {
            position: absolute;
            width: 150%;
            height: 150%;
            top: -25%;
            left: -25%;
            background: radial-gradient(circle, rgba(0,90,91,0.2) 0%, rgba(0,90,91,0) 70%);
            border-radius: 50%;
            z-index: 5;
            animation: pulse 4s infinite ease-in-out;
        }
        
        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0.3; }
            50% { transform: scale(1); opacity: 0.5; }
            100% { transform: scale(0.8); opacity: 0.3; }
        }
    </style>
</head>
<body class="font-sans text-gray-800 bg-white">
    <!-- Navigation -->
    <nav class="fixed w-full bg-white bg-opacity-95 shadow-md z-50 transition-all duration-300" id="navbar">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-2">
                    <div class="relative">
                        <div class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                            <span class="text-white font-serif font-bold text-xl">E</span>
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                            <span class="text-midnight-black font-serif font-bold text-xs">H</span>
                        </div>
                    </div>
                    <span class="text-deep-teal font-serif font-bold text-xl">EmBrace Holdings</span>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</a>
                    <a href="#about" class="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">About</a>
                    <a href="#services" class="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</a>
                    <a href="#impact" class="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Impact</a>
                    <a href="#contact" class="nav-link text-gray-700 hover:text-deep-teal font-medium transition-colors">Contact</a>
                    <button class="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
                        Investor Portal
                    </button>
                </div>
                
                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-deep-teal focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="md:hidden hidden pb-4">
                <div class="flex flex-col space-y-4">
                    <a href="#home" class="text-gray-700 hover:text-deep-teal font-medium transition-colors">Home</a>
                    <a href="#about" class="text-gray-700 hover:text-deep-teal font-medium transition-colors">About</a>
                    <a href="#services" class="text-gray-700 hover:text-deep-teal font-medium transition-colors">Services</a>
                    <a href="#impact" class="text-gray-700 hover:text-deep-teal font-medium transition-colors">Impact</a>
                    <a href="#contact" class="text-gray-700 hover:text-deep-teal font-medium transition-colors">Contact</a>
                    <button class="gold-gradient text-midnight-black px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all w-full">
                        Investor Portal
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-pattern min-h-screen flex items-center pt-20">
        <div class="container mx-auto px-4 md:px-6 lg:px-8 py-20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="text-white">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
                        <span class="block">Global Vision.</span>
                        <span class="block">Humanitarian Impact.</span>
                        <span class="block">Financial Excellence.</span>
                    </h1>
                    <div class="h-1 w-24 gold-gradient mb-6"></div>
                    <p class="text-lg md:text-xl opacity-90 mb-8 max-w-lg">
                        EmBrace Holdings Enterprise LLC drives economic development through strategic investments and humanitarian initiatives across the globe.
                    </p>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <button class="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-xl transition-all">
                            Our Services
                        </button>
                        <button class="bg-transparent border-2 border-white text-white px-8 py-3 rounded-full font-medium hover:bg-white hover:text-deep-teal transition-all">
                            Learn More
                        </button>
                    </div>
                </div>
                <div class="relative flex justify-center">
                    <div class="relative animate-float">
                        <svg class="w-64 h-64 md:w-80 md:h-80 text-white opacity-90 globe-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M2 12h20"></path>
                            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-20 h-20 gold-gradient rounded-full flex items-center justify-center">
                                <span class="text-midnight-black font-serif font-bold text-2xl">EH</span>
                            </div>
                        </div>
                        <div class="globe-bg"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="absolute bottom-0 left-0 right-0">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" class="w-full">
                <path fill="#ffffff" fill-opacity="1" d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
            </svg>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">About EmBrace Holdings</h2>
                <div class="section-divider mx-auto mb-6"></div>
                <p class="text-gray-600 max-w-3xl mx-auto">
                    A global leader in asset management, private equity, and project development with a humanitarian mission at our core.
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div class="order-2 lg:order-1">
                    <h3 class="text-2xl font-serif font-bold text-deep-teal mb-6">Our Vision & Mission</h3>
                    <p class="text-gray-600 mb-6">
                        EmBrace Holdings Enterprise LLC was founded on the principle that financial success and humanitarian impact can go hand in hand. We believe in creating sustainable economic growth while addressing global challenges.
                    </p>
                    <p class="text-gray-600 mb-8">
                        Our team of experts brings decades of experience in finance, development, and humanitarian work to create innovative solutions that benefit communities worldwide while delivering exceptional returns for our investors.
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gray-50 p-6 rounded-lg border-l-4 border-deep-teal">
                            <div class="text-deep-teal mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                            </div>
                            <h4 class="font-bold text-lg mb-2">Our Values</h4>
                            <p class="text-gray-600">Integrity, innovation, and impact guide every decision we make.</p>
                        </div>
                        <div class="bg-gray-50 p-6 rounded-lg border-l-4 border-royal-gold">
                            <div class="text-royal-gold mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </div>
                            <h4 class="font-bold text-lg mb-2">Our Approach</h4>
                            <p class="text-gray-600">Strategic investments with measurable social and financial returns.</p>
                        </div>
                    </div>
                </div>
                
                <div class="order-1 lg:order-2 relative">
                    <div class="relative z-10 rounded-lg overflow-hidden shadow-xl">
                        <div class="aspect-w-16 aspect-h-9 bg-deep-teal">
                            <svg class="w-full h-full text-white opacity-10" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                            </svg>
                        </div>
                        <div class="absolute inset-0 flex flex-col justify-center items-center text-white p-8">
                            <h3 class="text-2xl md:text-3xl font-serif font-bold mb-4 text-center">Global Presence</h3>
                            <p class="text-center mb-6">Operating in 30+ countries across 5 continents</p>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="text-center">
                                    <div class="text-royal-gold text-3xl font-bold">$2.7B</div>
                                    <div class="text-sm">Assets Under Management</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-royal-gold text-3xl font-bold">120+</div>
                                    <div class="text-sm">Projects Funded</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-royal-gold text-3xl font-bold">15+</div>
                                    <div class="text-sm">Years Experience</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-royal-gold text-3xl font-bold">5M+</div>
                                    <div class="text-sm">Lives Impacted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute -bottom-6 -right-6 w-64 h-64 bg-royal-gold opacity-10 rounded-full"></div>
                    <div class="absolute -top-6 -left-6 w-32 h-32 bg-deep-teal opacity-10 rounded-full"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Our Services</h2>
                <div class="section-divider mx-auto mb-6"></div>
                <p class="text-gray-600 max-w-3xl mx-auto">
                    Comprehensive financial solutions designed to create lasting value and positive global impact.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Asset Management -->
                <div class="service-card bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="h-2 gold-gradient"></div>
                    <div class="p-6">
                        <div class="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-serif font-bold text-deep-teal mb-3">Asset Management</h3>
                        <p class="text-gray-600 mb-6">
                            Strategic portfolio management with a focus on sustainable, long-term growth and social impact.
                        </p>
                        <ul class="text-gray-600 mb-6 space-y-2">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                ESG-Focused Investments
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Wealth Preservation
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Risk Management
                            </li>
                        </ul>
                        <a href="#" class="text-deep-teal font-medium hover:text-royal-gold transition-colors flex items-center">
                            Learn More
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Private Equity -->
                <div class="service-card bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="h-2 gold-gradient"></div>
                    <div class="p-6">
                        <div class="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-serif font-bold text-deep-teal mb-3">Private Equity</h3>
                        <p class="text-gray-600 mb-6">
                            Strategic investments in high-potential companies that drive innovation and social change.
                        </p>
                        <ul class="text-gray-600 mb-6 space-y-2">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Growth Capital
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Buyouts & Acquisitions
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Venture Capital
                            </li>
                        </ul>
                        <a href="#" class="text-deep-teal font-medium hover:text-royal-gold transition-colors flex items-center">
                            Learn More
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Project Development -->
                <div class="service-card bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="h-2 gold-gradient"></div>
                    <div class="p-6">
                        <div class="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-serif font-bold text-deep-teal mb-3">Project Development</h3>
                        <p class="text-gray-600 mb-6">
                            End-to-end management of complex projects that address critical global challenges.
                        </p>
                        <ul class="text-gray-600 mb-6 space-y-2">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Infrastructure Development
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Sustainable Energy
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Healthcare Facilities
                            </li>
                        </ul>
                        <a href="#" class="text-deep-teal font-medium hover:text-royal-gold transition-colors flex items-center">
                            Learn More
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Finance -->
                <div class="service-card bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="h-2 gold-gradient"></div>
                    <div class="p-6">
                        <div class="w-16 h-16 bg-deep-teal bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-serif font-bold text-deep-teal mb-3">Finance</h3>
                        <p class="text-gray-600 mb-6">
                            Innovative financial solutions that enable growth and create positive social impact.
                        </p>
                        <ul class="text-gray-600 mb-6 space-y-2">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Project Financing
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Structured Finance
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-royal-gold mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Impact Investing
                            </li>
                        </ul>
                        <a href="#" class="text-deep-teal font-medium hover:text-royal-gold transition-colors flex items-center">
                            Learn More
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact Section -->
    <section id="impact" class="py-20 bg-white">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Our Global Impact</h2>
                <div class="section-divider mx-auto mb-6"></div>
                <p class="text-gray-600 max-w-3xl mx-auto">
                    Creating sustainable change through strategic investments and humanitarian initiatives.
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div class="order-2 lg:order-1">
                    <div class="mb-8">
                        <h3 class="text-2xl font-serif font-bold text-deep-teal mb-4">Humanitarian Focus</h3>
                        <p class="text-gray-600 mb-4">
                            At EmBrace Holdings, we believe that financial success and humanitarian impact go hand in hand. Our projects are designed to address critical global challenges while delivering exceptional returns.
                        </p>
                        <p class="text-gray-600">
                            From sustainable infrastructure to healthcare facilities, our investments create lasting positive change in communities worldwide.
                        </p>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <h4 class="font-bold text-lg">Clean Energy</h4>
                            </div>
                            <p class="text-gray-600">
                                Financing renewable energy projects that provide clean power to underserved communities.
                            </p>
                        </div>
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                </div>
                                <h4 class="font-bold text-lg">Infrastructure</h4>
                            </div>
                            <p class="text-gray-600">
                                Building critical infrastructure that connects communities and enables economic growth.
                            </p>
                        </div>
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <h4 class="font-bold text-lg">Healthcare</h4>
                            </div>
                            <p class="text-gray-600">
                                Investing in healthcare facilities and technologies that improve access to quality care.
                            </p>
                        </div>
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                </div>
                                <h4 class="font-bold text-lg">Education</h4>
                            </div>
                            <p class="text-gray-600">
                                Supporting educational initiatives that empower communities through knowledge and skills.
                            </p>
                        </div>
                    </div>
                    
                    <button class="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-xl transition-all">
                        View Impact Report
                    </button>
                </div>
                
                <div class="order-1 lg:order-2">
                    <div class="relative">
                        <div class="bg-deep-teal rounded-lg p-8 text-white relative z-10">
                            <h3 class="text-2xl font-serif font-bold mb-6">Impact Metrics</h3>
                            
                            <div class="space-y-6">
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span>Clean Water Access</span>
                                        <span>85%</span>
                                    </div>
                                    <div class="w-full bg-white bg-opacity-20 rounded-full h-2">
                                        <div class="gold-gradient h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span>Renewable Energy</span>
                                        <span>72%</span>
                                    </div>
                                    <div class="w-full bg-white bg-opacity-20 rounded-full h-2">
                                        <div class="gold-gradient h-2 rounded-full" style="width: 72%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span>Healthcare Access</span>
                                        <span>68%</span>
                                    </div>
                                    <div class="w-full bg-white bg-opacity-20 rounded-full h-2">
                                        <div class="gold-gradient h-2 rounded-full" style="width: 68%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span>Education Initiatives</span>
                                        <span>91%</span>
                                    </div>
                                    <div class="w-full bg-white bg-opacity-20 rounded-full h-2">
                                        <div class="gold-gradient h-2 rounded-full" style="width: 91%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-8 pt-6 border-t border-white border-opacity-20">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <div class="text-royal-gold text-3xl font-bold">$420M</div>
                                        <div class="text-sm text-white text-opacity-80">Impact Investments</div>
                                    </div>
                                    <div>
                                        <div class="text-royal-gold text-3xl font-bold">27</div>
                                        <div class="text-sm text-white text-opacity-80">Countries Served</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="absolute top-4 right-4 -z-10 w-full h-full bg-royal-gold opacity-20 rounded-lg transform rotate-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">What Our Partners Say</h2>
                <div class="section-divider mx-auto mb-6"></div>
                <p class="text-gray-600 max-w-3xl mx-auto">
                    Hear from organizations and communities that have partnered with EmBrace Holdings.
                </p>
            </div>
            
            <div class="relative">
                <div class="overflow-hidden">
                    <div class="flex testimonial-slider" id="testimonial-slider">
                        <!-- Testimonial 1 -->
                        <div class="min-w-full px-4">
                            <div class="bg-white rounded-lg shadow-lg p-8 relative">
                                <div class="text-royal-gold mb-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 opacity-20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                                    </svg>
                                </div>
                                <p class="text-gray-600 text-lg mb-6">
                                    "EmBrace Holdings' investment in our renewable energy project has transformed our community. Not only did they provide the capital we needed, but their expertise and guidance were invaluable throughout the process."
                                </p>
                                <div class="flex items-center">
                                    <div class="w-12 h-12 rounded-full bg-deep-teal flex items-center justify-center text-white font-bold text-xl mr-4">
                                        SE
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-deep-teal">Sarah Edwards</h4>
                                        <p class="text-gray-500">CEO, GreenPower Initiatives</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Testimonial 2 -->
                        <div class="min-w-full px-4">
                            <div class="bg-white rounded-lg shadow-lg p-8 relative">
                                <div class="text-royal-gold mb-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 opacity-20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                                    </svg>
                                </div>
                                <p class="text-gray-600 text-lg mb-6">
                                    "The team at EmBrace Holdings understands that true impact requires both financial acumen and a deep commitment to humanitarian values. Their approach to our healthcare infrastructure project was comprehensive and visionary."
                                </p>
                                <div class="flex items-center">
                                    <div class="w-12 h-12 rounded-full bg-deep-teal flex items-center justify-center text-white font-bold text-xl mr-4">
                                        MJ
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-deep-teal">Dr. Michael Johnson</h4>
                                        <p class="text-gray-500">Director, Global Health Partners</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Testimonial 3 -->
                        <div class="min-w-full px-4">
                            <div class="bg-white rounded-lg shadow-lg p-8 relative">
                                <div class="text-royal-gold mb-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 opacity-20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                                    </svg>
                                </div>
                                <p class="text-gray-600 text-lg mb-6">
                                    "Working with EmBrace Holdings has been transformative for our educational initiative. Their strategic approach to financing and development has allowed us to expand our reach and impact in ways we never thought possible."
                                </p>
                                <div class="flex items-center">
                                    <div class="w-12 h-12 rounded-full bg-deep-teal flex items-center justify-center text-white font-bold text-xl mr-4">
                                        AL
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-deep-teal">Amara Lawal</h4>
                                        <p class="text-gray-500">Founder, Education Without Borders</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-center mt-8 space-x-2">
                    <button class="w-3 h-3 rounded-full bg-deep-teal bg-opacity-30 focus:outline-none testimonial-dot active" data-index="0"></button>
                    <button class="w-3 h-3 rounded-full bg-deep-teal bg-opacity-30 focus:outline-none testimonial-dot" data-index="1"></button>
                    <button class="w-3 h-3 rounded-full bg-deep-teal bg-opacity-30 focus:outline-none testimonial-dot" data-index="2"></button>
                </div>
                
                <button id="prev-testimonial" class="absolute top-1/2 left-0 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-lg focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                
                <button id="next-testimonial" class="absolute top-1/2 right-0 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-lg focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Get in Touch</h2>
                <div class="section-divider mx-auto mb-6"></div>
                <p class="text-gray-600 max-w-3xl mx-auto">
                    Connect with our team to explore partnership opportunities and learn more about our services.
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <div class="bg-gray-50 rounded-lg p-8 h-full">
                        <h3 class="text-2xl font-serif font-bold text-deep-teal mb-6">Contact Information</h3>
                        
                        <div class="space-y-6">
                            <div class="flex items-start">
                                <div class="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-bold text-deep-teal mb-1">Global Headquarters</h4>
                                    <p class="text-gray-600">
                                        One EmBrace Tower<br>
                                        1200 Financial District<br>
                                        New York, NY 10004<br>
                                        United States
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-bold text-deep-teal mb-1">Phone</h4>
                                    <p class="text-gray-600">
                                        +****************<br>
                                        +1 (800) EMBRACE
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-bold text-deep-teal mb-1">Email</h4>
                                    <p class="text-gray-600">
                                        <EMAIL><br>
                                        <EMAIL>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-10 h-10 rounded-full bg-deep-teal bg-opacity-10 flex items-center justify-center mr-4 mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-deep-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-bold text-deep-teal mb-1">Business Hours</h4>
                                    <p class="text-gray-600">
                                        Monday - Friday: 9:00 AM - 6:00 PM EST<br>
                                        Saturday - Sunday: Closed
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <h4 class="font-bold text-deep-teal mb-4">Connect With Us</h4>
                            <div class="flex space-x-4">
                                <a href="#" class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center text-white hover:bg-royal-gold transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                                    </svg>
                                </a>
                                <a href="#" class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center text-white hover:bg-royal-gold transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                    </svg>
                                </a>
                                <a href="#" class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center text-white hover:bg-royal-gold transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"/>
                                    </svg>
                                </a>
                                <a href="#" class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center text-white hover:bg-royal-gold transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <form class="bg-gray-50 rounded-lg p-8">
                        <h3 class="text-2xl font-serif font-bold text-deep-teal mb-6">Send Us a Message</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="first_name" class="block text-gray-700 font-medium mb-2">First Name</label>
                                <input type="text" id="first_name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-deep-teal focus:border-transparent" placeholder="Enter your first name">
                            </div>
                            <div>
                                <label for="last_name" class="block text-gray-700 font-medium mb-2">Last Name</label>
                                <input type="text" id="last_name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-deep-teal focus:border-transparent" placeholder="Enter your last name">
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label for="email" class="block text-gray-700 font-medium mb-2">Email Address</label>
                            <input type="email" id="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-deep-teal focus:border-transparent" placeholder="Enter your email address">
                        </div>
                        
                        <div class="mb-6">
                            <label for="phone" class="block text-gray-700 font-medium mb-2">Phone Number</label>
                            <input type="tel" id="phone" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-deep-teal focus:border-transparent" placeholder="Enter your phone number">
                        </div>
                        
                        <div class="mb-6">
                            <label for="subject" class="block text-gray-700 font-medium mb-2">Subject</label>
                            <select id="subject" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-deep-teal focus:border-transparent">
                                <option value="" selected disabled>Select a subject</option>
                                <option value="investment">Investment Opportunities</option>
                                <option value="partnership">Partnership Inquiry</option>
                                <option value="project">Project Proposal</option>
                                <option value="career">Career Opportunities</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-6">
                            <label for="message" class="block text-gray-700 font-medium mb-2">Message</label>
                            <textarea id="message" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-deep-teal focus:border-transparent" placeholder="Enter your message"></textarea>
                        </div>
                        
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox h-5 w-5 text-deep-teal">
                                <span class="ml-2 text-gray-700">I agree to the <a href="#" class="text-deep-teal hover:underline">Privacy Policy</a> and <a href="#" class="text-deep-teal hover:underline">Terms of Service</a>.</span>
                            </label>
                        </div>
                        
                        <button type="submit" class="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-xl transition-all w-full">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Global Presence Map -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-serif font-bold text-deep-teal mb-4">Our Global Presence</h2>
                <div class="section-divider mx-auto mb-6"></div>
                <p class="text-gray-600 max-w-3xl mx-auto">
                    EmBrace Holdings operates across the globe, with a focus on regions where our investments can create the greatest impact.
                </p>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg p-6 md:p-10">
                <div class="relative">
                    <svg class="w-full h-auto" viewBox="0 0 1000 500" xmlns="http://www.w3.org/2000/svg">
                        <!-- World Map (simplified) -->
                        <path d="M150,100 Q250,50 350,100 T550,100 T750,100 T950,100 V400 Q850,450 750,400 T550,400 T350,400 T150,400 Z" fill="#E6F4F1" stroke="#005A5B" stroke-width="2" />
                        
                        <!-- North America -->
                        <path d="M200,120 Q220,150 240,130 T280,150 T320,140 V220 Q300,240 280,230 T240,250 T200,240 Z" fill="#005A5B" fill-opacity="0.7" />
                        
                        <!-- South America -->
                        <path d="M280,260 Q290,280 300,270 T320,290 T340,280 V350 Q330,370 320,360 T300,380 T280,370 Z" fill="#005A5B" fill-opacity="0.7" />
                        
                        <!-- Europe -->
                        <path d="M450,130 Q460,150 470,140 T490,160 T510,150 V200 Q500,220 490,210 T470,230 T450,220 Z" fill="#005A5B" fill-opacity="0.7" />
                        
                        <!-- Africa -->
                        <path d="M470,240 Q480,260 490,250 T510,270 T530,260 V330 Q520,350 510,340 T490,360 T470,350 Z" fill="#005A5B" fill-opacity="0.7" />
                        
                        <!-- Asia -->
                        <path d="M580,130 Q600,150 620,140 T660,160 T700,150 V250 Q680,270 660,260 T620,280 T580,270 Z" fill="#005A5B" fill-opacity="0.7" />
                        
                        <!-- Australia -->
                        <path d="M750,300 Q760,320 770,310 T790,330 T810,320 V360 Q800,380 790,370 T770,390 T750,380 Z" fill="#005A5B" fill-opacity="0.7" />
                        
                        <!-- Location Markers -->
                        <!-- New York -->
                        <circle cx="250" cy="170" r="8" class="gold-gradient" />
                        <text x="250" y="155" text-anchor="middle" fill="#005A5B" font-weight="bold" font-size="12">New York</text>
                        
                        <!-- London -->
                        <circle cx="470" cy="160" r="8" class="gold-gradient" />
                        <text x="470" y="145" text-anchor="middle" fill="#005A5B" font-weight="bold" font-size="12">London</text>
                        
                        <!-- Dubai -->
                        <circle cx="550" cy="210" r="8" class="gold-gradient" />
                        <text x="550" y="195" text-anchor="middle" fill="#005A5B" font-weight="bold" font-size="12">Dubai</text>
                        
                        <!-- Singapore -->
                        <circle cx="650" cy="250" r="8" class="gold-gradient" />
                        <text x="650" y="235" text-anchor="middle" fill="#005A5B" font-weight="bold" font-size="12">Singapore</text>
                        
                        <!-- Nairobi -->
                        <circle cx="510" cy="280" r="8" class="gold-gradient" />
                        <text x="510" y="265" text-anchor="middle" fill="#005A5B" font-weight="bold" font-size="12">Nairobi</text>
                        
                        <!-- São Paulo -->
                        <circle cx="320" cy="320" r="8" class="gold-gradient" />
                        <text x="320" y="305" text-anchor="middle" fill="#005A5B" font-weight="bold" font-size="12">São Paulo</text>
                        
                        <!-- Sydney -->
                        <circle cx="780" cy="340" r="8" class="gold-gradient" />
                        <text x="780" y="325" text-anchor="middle" fill="#005A5B" font-weight="bold" font-size="12">Sydney</text>
                    </svg>
                    
                    <div class="absolute bottom-4 right-4 bg-white bg-opacity-80 p-4 rounded-lg">
                        <div class="flex items-center mb-2">
                            <div class="w-4 h-4 rounded-full gold-gradient mr-2"></div>
                            <span class="text-sm text-gray-700">Office Locations</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-deep-teal opacity-70 mr-2"></div>
                            <span class="text-sm text-gray-700">Active Regions</span>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                    <div class="text-center">
                        <div class="text-deep-teal text-3xl font-bold">7</div>
                        <div class="text-gray-600 text-sm">Global Offices</div>
                    </div>
                    <div class="text-center">
                        <div class="text-deep-teal text-3xl font-bold">30+</div>
                        <div class="text-gray-600 text-sm">Countries</div>
                    </div>
                    <div class="text-center">
                        <div class="text-deep-teal text-3xl font-bold">120+</div>
                        <div class="text-gray-600 text-sm">Active Projects</div>
                    </div>
                    <div class="text-center">
                        <div class="text-deep-teal text-3xl font-bold">500+</div>
                        <div class="text-gray-600 text-sm">Team Members</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-deep-teal text-white">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-serif font-bold mb-6">Ready to Make a Global Impact?</h2>
                <p class="text-xl opacity-90 mb-8">
                    Join us in our mission to create sustainable economic growth and positive humanitarian impact worldwide.
                </p>
                <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <button class="gold-gradient text-midnight-black px-8 py-3 rounded-full font-medium hover:shadow-xl transition-all">
                        Partner With Us
                    </button>
                    <button class="bg-transparent border-2 border-white text-white px-8 py-3 rounded-full font-medium hover:bg-white hover:text-deep-teal transition-all">
                        Download Brochure
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-midnight-black text-white pt-16 pb-8">
        <div class="container mx-auto px-4 md:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <div>
                    <div class="flex items-center space-x-2 mb-6">
                        <div class="relative">
                            <div class="w-10 h-10 rounded-full bg-deep-teal flex items-center justify-center">
                                <span class="text-white font-serif font-bold text-xl">E</span>
                            </div>
                            <div class="absolute -bottom-1 -right-1 w-6 h-6 rounded-full gold-gradient flex items-center justify-center">
                                <span class="text-midnight-black font-serif font-bold text-xs">H</span>
                            </div>
                        </div>
                        <span class="text-white font-serif font-bold text-xl">EmBrace Holdings</span>
                    </div>
                    <p class="text-gray-400 mb-6">
                        Global vision. Humanitarian impact. Financial excellence. EmBrace Holdings Enterprise LLC is committed to creating sustainable economic growth and positive social change worldwide.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-serif font-bold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="#home" class="text-gray-400 hover:text-royal-gold transition-colors">Home</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-royal-gold transition-colors">About Us</a></li>
                        <li><a href="#services" class="text-gray-400 hover:text-royal-gold transition-colors">Our Services</a></li>
                        <li><a href="#impact" class="text-gray-400 hover:text-royal-gold transition-colors">Global Impact</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-royal-gold transition-colors">Contact Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">Investor Portal</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-serif font-bold mb-6">Our Services</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">Asset Management</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">Private Equity</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">Project Development</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">Finance Solutions</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">Impact Investing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-royal-gold transition-colors">ESG Advisory</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-serif font-bold mb-6">Newsletter</h4>
                    <p class="text-gray-400 mb-4">
                        Subscribe to our newsletter for the latest updates on our projects and impact initiatives.
                    </p>
                    <form class="mb-4">
                        <div class="flex">
                            <input type="email" placeholder="Your email address" class="px-4 py-2 rounded-l-lg w-full focus:outline-none text-gray-800">
                            <button type="submit" class="gold-gradient text-midnight-black px-4 py-2 rounded-r-lg hover:shadow-lg transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </button>
                        </div>
                    </form>
                    <p class="text-gray-500 text-sm">
                        By subscribing, you agree to our Privacy Policy and consent to receive updates from EmBrace Holdings.
                    </p>
                </div>
            </div>
            
            <div class="border-t border-gray-800 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-500 text-sm mb-4 md:mb-0">
                        &copy; 2023 EmBrace Holdings Enterprise LLC. All rights reserved.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-500 hover:text-royal-gold transition-colors text-sm">Privacy Policy</a>
                        <a href="#" class="text-gray-500 hover:text-royal-gold transition-colors text-sm">Terms of Service</a>
                        <a href="#" class="text-gray-500 hover:text-royal-gold transition-colors text-sm">Cookie Policy</a>
                        <a href="#" class="text-gray-500 hover:text-royal-gold transition-colors text-sm">Sitemap</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile Menu Toggle
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Navbar Scroll Effect
        const navbar = document.getElementById('navbar');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                navbar.classList.add('py-2');
                navbar.classList.remove('py-4');
            } else {
                navbar.classList.add('py-4');
                navbar.classList.remove('py-2');
            }
        });
        
        // Smooth Scrolling for Anchor Links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });
        
        // Testimonial Slider
        const testimonialSlider = document.getElementById('testimonial-slider');
        const prevButton = document.getElementById('prev-testimonial');
        const nextButton = document.getElementById('next-testimonial');
        const dots = document.querySelectorAll('.testimonial-dot');
        let currentIndex = 0;
        
        function updateSlider() {
            testimonialSlider.style.transform = `translateX(-${currentIndex * 100}%)`;
            
            // Update active dot
            dots.forEach((dot, index) => {
                if (index === currentIndex) {
                    dot.classList.add('bg-deep-teal');
                    dot.classList.remove('bg-opacity-30');
                } else {
                    dot.classList.remove('bg-deep-teal');
                    dot.classList.add('bg-opacity-30');
                }
            });
        }
        
        prevButton.addEventListener('click', () => {
            currentIndex = (currentIndex === 0) ? dots.length - 1 : currentIndex - 1;
            updateSlider();
        });
        
        nextButton.addEventListener('click', () => {
            currentIndex = (currentIndex === dots.length - 1) ? 0 : currentIndex + 1;
            updateSlider();
        });
        
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentIndex = index;
                updateSlider();
            });
        });
        
        // Auto-rotate testimonials
        setInterval(() => {
            currentIndex = (currentIndex === dots.length - 1) ? 0 : currentIndex + 1;
            updateSlider();
        }, 8000);
        
        // Form Submission (Demo)
        const contactForm = document.querySelector('form');
        
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();
            alert('Thank you for your message! This is a demo form. In a real implementation, your message would be sent to EmBrace Holdings.');
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'95cbd0c2e599586e',t:'MTc1MjEwNTk4OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
